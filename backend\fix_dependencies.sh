#!/bin/bash

# LotusDream 依赖修复脚本

echo "🔧 修复 LotusDream 项目依赖问题..."

# 停止所有容器
echo "⏹️  停止现有容器..."
docker-compose down

# 清理 Docker 缓存
echo "🧹 清理 Docker 缓存..."
docker system prune -f

# 重新构建镜像（不使用缓存）
echo "🔨 重新构建镜像..."
docker-compose build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo "✅ 修复完成！"
echo "📝 接下来的步骤："
echo "1. 运行: docker-compose logs api 查看API日志"
echo "2. 访问: http://localhost:8000/health 检查健康状态"
echo "3. 访问: http://localhost:8000/docs 查看API文档"
