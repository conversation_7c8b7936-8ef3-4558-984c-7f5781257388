"""
Rate limiting middleware
"""

from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable
import time
import structlog

from app.core.redis import get_redis, CacheKeys
from app.core.config import settings
from app.core.exceptions import RateLimitException

logger = structlog.get_logger()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using Redis"""
    
    def __init__(self, app):
        super().__init__(app)
        self.redis = None
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with rate limiting"""
        
        # Skip rate limiting for health checks and docs
        if request.url.path in ["/health", "/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        try:
            # Get Redis client
            if self.redis is None:
                self.redis = get_redis()
            
            # Get client identifier (IP address or user ID if authenticated)
            client_id = await self._get_client_identifier(request)
            
            # Check rate limit
            await self._check_rate_limit(client_id, request.url.path)
            
            # Process request
            response = await call_next(request)
            
            return response
            
        except RateLimitException as e:
            raise e
        except Exception as e:
            logger.error("Rate limiting error", error=str(e))
            # Continue without rate limiting if Redis is unavailable
            return await call_next(request)
    
    async def _get_client_identifier(self, request: Request) -> str:
        """Get client identifier for rate limiting"""
        
        # Try to get user ID from token
        try:
            authorization = request.headers.get("Authorization")
            if authorization and authorization.startswith("Bearer "):
                from app.core.security import jwt_manager
                token = authorization.split(" ")[1]
                payload = jwt_manager.verify_token(token)
                user_id = payload.get("sub")
                if user_id:
                    return f"user:{user_id}"
        except Exception:
            pass
        
        # Fall back to IP address
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
    
    async def _check_rate_limit(self, client_id: str, path: str) -> None:
        """Check if client has exceeded rate limit"""
        
        # Different rate limits for different endpoints
        rate_limits = {
            "/v1/auth/login": (5, 300),  # 5 requests per 5 minutes
            "/v1/auth/register": (3, 3600),  # 3 requests per hour
            "/v1/dreams": (60, 3600),  # 60 requests per hour
            "/v1/analysis": (10, 3600),  # 10 analysis requests per hour
        }
        
        # Default rate limit
        default_limit = (settings.RATE_LIMIT_REQUESTS, settings.RATE_LIMIT_WINDOW)
        
        # Get rate limit for this path
        limit, window = default_limit
        for pattern, (pattern_limit, pattern_window) in rate_limits.items():
            if path.startswith(pattern):
                limit, window = pattern_limit, pattern_window
                break
        
        # Create cache key
        cache_key = CacheKeys.rate_limit(f"{client_id}:{path}")
        
        try:
            # Get current count
            current_count = await self.redis.get(cache_key)
            
            if current_count is None:
                # First request in window
                await self.redis.setex(cache_key, window, 1)
            else:
                current_count = int(current_count)
                
                if current_count >= limit:
                    # Rate limit exceeded
                    ttl = await self.redis.ttl(cache_key)
                    raise RateLimitException(
                        f"Rate limit exceeded. Try again in {ttl} seconds.",
                        details=[{
                            "limit": limit,
                            "window": window,
                            "retry_after": ttl
                        }]
                    )
                
                # Increment count
                await self.redis.incr(cache_key)
                
        except RateLimitException:
            raise
        except Exception as e:
            logger.error("Rate limit check failed", error=str(e))
            # Allow request if Redis check fails


class IPWhitelistMiddleware(BaseHTTPMiddleware):
    """IP whitelist middleware for admin endpoints"""
    
    def __init__(self, app, whitelist: list = None):
        super().__init__(app)
        self.whitelist = whitelist or []
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Check IP whitelist for admin endpoints"""
        
        # Only apply to admin endpoints
        if not request.url.path.startswith("/admin"):
            return await call_next(request)
        
        # Skip if no whitelist configured
        if not self.whitelist:
            return await call_next(request)
        
        # Get client IP
        client_ip = request.client.host
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        # Check whitelist
        if client_ip not in self.whitelist:
            logger.warning("Blocked request from non-whitelisted IP", ip=client_ip, path=request.url.path)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return await call_next(request)
