# LotusDream APP - 后台接口文档

## 📋 接口概述

**项目名称**：LotusDream APP 后台接口
**版本**：v1.0.0
**基础URL**：`https://api.lotusdream.com/v1`
**认证方式**：JWT Token
**数据格式**：JSON

## 🔐 认证说明

所有接口请求需要在Header中携带认证信息：
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

## 📱 接口分类

### 1. 用户认证模块
### 2. 梦境记录模块
### 3. 梦境解析模块
### 4. 历史记录模块
### 5. 数据统计模块
### 6. 用户设置模块

---

## 🔑 1. 用户认证模块

### 1.1 用户注册
**接口地址**：`POST /auth/register`
**接口描述**：用户注册账号

**请求参数**：
```json
{
  "username": "string",     // 用户名，3-20字符
  "email": "string",        // 邮箱地址
  "password": "string",     // 密码，6-20字符
  "device_id": "string"     // 设备唯一标识
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": "12345",
    "username": "dreamuser",
    "email": "<EMAIL>",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  }
}
```

### 1.2 用户登录
**接口地址**：`POST /auth/login`
**接口描述**：用户登录获取Token

**请求参数**：
```json
{
  "email": "string",        // 邮箱地址
  "password": "string",     // 密码
  "device_id": "string"     // 设备唯一标识
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": "12345",
    "username": "dreamuser",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400,
    "last_login": "2024-01-15T10:30:00Z"
  }
}
```

### 1.3 刷新Token
**接口地址**：`POST /auth/refresh`
**接口描述**：刷新用户Token

**请求参数**：
```json
{
  "refresh_token": "string"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  }
}
```

---

## 🌙 2. 梦境记录模块

### 2.1 创建梦境记录
**接口地址**：`POST /dreams`
**接口描述**：用户创建新的梦境记录

**请求参数**：
```json
{
  "dream_date": "2024-01-15",           // 梦境日期，格式：YYYY-MM-DD
  "emotions": ["平静", "快乐"],          // 情绪标签数组，1-3个
  "description": "string",              // 梦境描述，最大300字符
  "timezone": "Asia/Shanghai"           // 时区信息
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "梦境记录创建成功",
  "data": {
    "dream_id": "dream_67890",
    "user_id": "12345",
    "dream_date": "2024-01-15",
    "emotions": ["平静", "快乐"],
    "description": "我梦见自己在一个美丽的花园里...",
    "status": "pending_analysis",        // 状态：pending_analysis, analyzed
    "created_at": "2024-01-15T08:30:00Z",
    "updated_at": "2024-01-15T08:30:00Z"
  }
}
```

### 2.2 更新梦境记录
**接口地址**：`PUT /dreams/{dream_id}`
**接口描述**：更新指定的梦境记录

**请求参数**：
```json
{
  "dream_date": "2024-01-15",           // 可选
  "emotions": ["平静", "快乐", "兴奋"],   // 可选
  "description": "string"               // 可选
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "梦境记录更新成功",
  "data": {
    "dream_id": "dream_67890",
    "updated_at": "2024-01-15T09:15:00Z"
  }
}
```

### 2.3 删除梦境记录
**接口地址**：`DELETE /dreams/{dream_id}`
**接口描述**：删除指定的梦境记录

**响应示例**：
```json
{
  "code": 200,
  "message": "梦境记录删除成功",
  "data": {
    "dream_id": "dream_67890",
    "deleted_at": "2024-01-15T10:00:00Z"
  }
}
```

---

## 🧠 3. 梦境解析模块

### 3.1 提交梦境解析
**接口地址**：`POST /dreams/{dream_id}/analyze`
**接口描述**：提交梦境进行AI解析

**请求参数**：
```json
{
  "analysis_type": "comprehensive"      // 解析类型：quick, comprehensive
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "解析请求已提交",
  "data": {
    "analysis_id": "analysis_11111",
    "dream_id": "dream_67890",
    "status": "processing",              // 状态：processing, completed, failed
    "estimated_time": 30                 // 预计完成时间（秒）
  }
}
```

### 3.2 获取解析结果
**接口地址**：`GET /dreams/{dream_id}/analysis`
**接口描述**：获取梦境解析结果

**响应示例**：
```json
{
  "code": 200,
  "message": "获取解析结果成功",
  "data": {
    "analysis_id": "analysis_11111",
    "dream_id": "dream_67890",
    "status": "completed",
    "keywords": ["莲花", "花园", "宁静"],
    "psychological_hints": "莲花在梦境中通常象征着内心的纯净与成长...",
    "symbolic_meaning": "花园代表你的内心世界，而盛开的莲花象征着...",
    "suggestions": [
      "尝试冥想或瑜伽，培养内心的平静",
      "多接触大自然，感受生命的美好",
      "记录每日感悟，观察内心的变化"
    ],
    "poetry_quote": {
      "content": "莲花不染淤泥，心境自然清明",
      "description": "愿你如莲花般，在浮世中保持内心的纯净"
    },
    "confidence_score": 0.85,            // 解析置信度 0-1
    "analyzed_at": "2024-01-15T08:35:00Z"
  }
}
```

### 3.3 保存解析结果
**接口地址**：`POST /dreams/{dream_id}/analysis/save`
**接口描述**：保存解析结果到历史记录

**响应示例**：
```json
{
  "code": 200,
  "message": "解析结果保存成功",
  "data": {
    "dream_id": "dream_67890",
    "saved_at": "2024-01-15T08:40:00Z"
  }
}
```

---

## 📚 4. 历史记录模块

### 4.1 获取梦境历史列表
**接口地址**：`GET /dreams/history`
**接口描述**：获取用户的梦境历史记录列表

**请求参数**：
```
page: int           // 页码，默认1
limit: int          // 每页数量，默认20，最大100
sort: string        // 排序方式：date_desc, date_asc
status: string      // 筛选状态：all, analyzed, pending
emotion: string     // 筛选情绪
keyword: string     // 搜索关键词
start_date: string  // 开始日期 YYYY-MM-DD
end_date: string    // 结束日期 YYYY-MM-DD
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取历史记录成功",
  "data": {
    "total": 42,
    "page": 1,
    "limit": 20,
    "total_pages": 3,
    "dreams": [
      {
        "dream_id": "dream_67890",
        "dream_date": "2024-01-15",
        "emotions": ["平静", "快乐"],
        "description_preview": "我梦见自己在一个美丽的花园里...",
        "keywords": ["莲花", "花园", "宁静"],
        "status": "analyzed",
        "created_at": "2024-01-15T08:30:00Z"
      }
    ]
  }
}
```

### 4.2 获取梦境详情
**接口地址**：`GET /dreams/{dream_id}`
**接口描述**：获取指定梦境的详细信息

**响应示例**：
```json
{
  "code": 200,
  "message": "获取梦境详情成功",
  "data": {
    "dream_id": "dream_67890",
    "user_id": "12345",
    "dream_date": "2024-01-15",
    "emotions": ["平静", "快乐"],
    "description": "我梦见自己在一个美丽的花园里，到处都是盛开的莲花...",
    "status": "analyzed",
    "analysis": {
      "keywords": ["莲花", "花园", "宁静"],
      "psychological_hints": "莲花在梦境中通常象征着内心的纯净与成长...",
      "symbolic_meaning": "花园代表你的内心世界，而盛开的莲花象征着...",
      "suggestions": [
        "尝试冥想或瑜伽，培养内心的平静",
        "多接触大自然，感受生命的美好",
        "记录每日感悟，观察内心的变化"
      ],
      "poetry_quote": {
        "content": "莲花不染淤泥，心境自然清明",
        "description": "愿你如莲花般，在浮世中保持内心的纯净"
      },
      "confidence_score": 0.85,
      "analyzed_at": "2024-01-15T08:35:00Z"
    },
    "created_at": "2024-01-15T08:30:00Z",
    "updated_at": "2024-01-15T08:35:00Z"
  }
}
```

### 4.3 搜索梦境记录
**接口地址**：`GET /dreams/search`
**接口描述**：根据关键词搜索梦境记录

**请求参数**：
```
q: string           // 搜索关键词
page: int           // 页码，默认1
limit: int          // 每页数量，默认20
```

**响应示例**：
```json
{
  "code": 200,
  "message": "搜索完成",
  "data": {
    "total": 5,
    "page": 1,
    "limit": 20,
    "keyword": "莲花",
    "dreams": [
      {
        "dream_id": "dream_67890",
        "dream_date": "2024-01-15",
        "emotions": ["平静", "快乐"],
        "description_preview": "我梦见自己在一个美丽的花园里...",
        "keywords": ["莲花", "花园", "宁静"],
        "match_score": 0.95,
        "created_at": "2024-01-15T08:30:00Z"
      }
    ]
  }
}
```

---

## 📊 5. 数据统计模块

### 5.1 获取统计概览
**接口地址**：`GET /statistics/overview`
**接口描述**：获取用户的统计数据概览

**请求参数**：
```
period: string      // 统计周期：week, month, year
timezone: string    // 时区，默认 Asia/Shanghai
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取统计数据成功",
  "data": {
    "period": "month",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "summary": {
      "total_dreams": 15,
      "analyzed_dreams": 12,
      "usage_days": 28,
      "longest_streak": 7,
      "health_score": 78
    },
    "emotion_distribution": {
      "positive": 65,      // 积极情绪占比 %
      "neutral": 25,       // 中性情绪占比 %
      "negative": 10       // 消极情绪占比 %
    },
    "top_emotions": [
      {"emotion": "平静", "count": 8},
      {"emotion": "快乐", "count": 6},
      {"emotion": "焦虑", "count": 4}
    ],
    "top_keywords": [
      {"keyword": "莲花", "count": 5, "weight": 1.0},
      {"keyword": "飞翔", "count": 4, "weight": 0.8},
      {"keyword": "家人", "count": 3, "weight": 0.6}
    ]
  }
}
```

### 5.2 获取梦境频率统计
**接口地址**：`GET /statistics/frequency`
**接口描述**：获取梦境记录频率统计数据

**请求参数**：
```
period: string      // 统计周期：week, month, year
timezone: string    // 时区
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取频率统计成功",
  "data": {
    "period": "month",
    "frequency_data": [
      {"date": "2024-01-01", "count": 1},
      {"date": "2024-01-02", "count": 0},
      {"date": "2024-01-03", "count": 2},
      {"date": "2024-01-04", "count": 1}
    ],
    "weekly_average": 3.8,
    "monthly_total": 15,
    "peak_day": "2024-01-15",
    "peak_count": 3
  }
}
```

### 5.3 获取情绪趋势
**接口地址**：`GET /statistics/emotions`
**接口描述**：获取情绪变化趋势数据

**请求参数**：
```
period: string      // 统计周期：week, month, year
timezone: string    // 时区
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取情绪趋势成功",
  "data": {
    "period": "month",
    "emotion_trends": [
      {
        "date": "2024-01-01",
        "positive_score": 0.7,
        "neutral_score": 0.2,
        "negative_score": 0.1
      },
      {
        "date": "2024-01-02",
        "positive_score": 0.8,
        "neutral_score": 0.1,
        "negative_score": 0.1
      }
    ],
    "overall_trend": "improving",    // improving, stable, declining
    "health_score_history": [
      {"date": "2024-01-01", "score": 75},
      {"date": "2024-01-08", "score": 78},
      {"date": "2024-01-15", "score": 82}
    ]
  }
}
```

### 5.4 获取关键词云数据
**接口地址**：`GET /statistics/keywords`
**接口描述**：获取关键词云图数据

**请求参数**：
```
period: string      // 统计周期：week, month, year
limit: int          // 返回关键词数量，默认50
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取关键词数据成功",
  "data": {
    "period": "month",
    "keywords": [
      {"keyword": "莲花", "count": 5, "weight": 1.0, "category": "nature"},
      {"keyword": "飞翔", "count": 4, "weight": 0.8, "category": "action"},
      {"keyword": "家人", "count": 3, "weight": 0.6, "category": "relationship"}
    ],
    "categories": {
      "nature": 8,
      "action": 6,
      "relationship": 4,
      "emotion": 3
    }
  }
}
```

---

## ⚙️ 6. 用户设置模块

### 6.1 获取用户信息
**接口地址**：`GET /user/profile`
**接口描述**：获取用户基本信息

**响应示例**：
```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "user_id": "12345",
    "username": "dreamuser",
    "email": "<EMAIL>",
    "avatar_url": "https://example.com/avatar.jpg",
    "registration_date": "2024-01-01T00:00:00Z",
    "last_login": "2024-01-15T10:30:00Z",
    "statistics": {
      "total_dreams": 42,
      "total_analyzed": 38,
      "usage_days": 28,
      "member_since_days": 15
    },
    "settings": {
      "timezone": "Asia/Shanghai",
      "language": "zh-CN",
      "notification_enabled": true,
      "privacy_mode": false
    }
  }
}
```

### 6.2 更新用户信息
**接口地址**：`PUT /user/profile`
**接口描述**：更新用户基本信息

**请求参数**：
```json
{
  "username": "string",        // 可选
  "avatar_url": "string",      // 可选
  "timezone": "string",        // 可选
  "language": "string"         // 可选
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": {
    "user_id": "12345",
    "updated_at": "2024-01-15T11:00:00Z"
  }
}
```

### 6.3 数据导出
**接口地址**：`POST /user/export`
**接口描述**：导出用户数据

**请求参数**：
```json
{
  "format": "json",            // 导出格式：json, csv
  "include_analysis": true,    // 是否包含解析结果
  "date_range": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  }
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "数据导出请求已提交",
  "data": {
    "export_id": "export_99999",
    "status": "processing",
    "estimated_time": 60,
    "download_url": null
  }
}
```

### 6.4 获取导出状态
**接口地址**：`GET /user/export/{export_id}`
**接口描述**：获取数据导出状态

**响应示例**：
```json
{
  "code": 200,
  "message": "获取导出状态成功",
  "data": {
    "export_id": "export_99999",
    "status": "completed",       // processing, completed, failed
    "download_url": "https://api.lotusdream.com/downloads/export_99999.json",
    "file_size": 1024576,        // 文件大小（字节）
    "expires_at": "2024-01-16T11:00:00Z"
  }
}
```

### 6.5 删除用户账号
**接口地址**：`DELETE /user/account`
**接口描述**：删除用户账号及所有数据

**请求参数**：
```json
{
  "password": "string",        // 确认密码
  "confirmation": "DELETE"     // 确认删除
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "账号删除成功",
  "data": {
    "user_id": "12345",
    "deleted_at": "2024-01-15T12:00:00Z"
  }
}
```

---

## 📝 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  }
}
```

### 状态码说明
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权/Token无效
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率限制
- `500` - 服务器内部错误

---

## 🔒 安全说明

### 1. 数据加密
- 所有敏感数据传输使用HTTPS加密
- 用户密码使用bcrypt加密存储
- 梦境内容在数据库中加密存储

### 2. 访问控制
- 用户只能访问自己的数据
- Token有效期为24小时
- 支持Token刷新机制

### 3. 隐私保护
- 梦境数据本地优先存储
- 服务器数据定期清理
- 支持完全删除用户数据

### 4. 频率限制
- 登录接口：5次/分钟
- 解析接口：10次/小时
- 其他接口：100次/分钟

---

## 📞 技术支持

**开发团队**：LotusDream Tech Team
**技术文档**：https://docs.lotusdream.com
**问题反馈**：<EMAIL>
**更新日志**：https://changelog.lotusdream.com
```
