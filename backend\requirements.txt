# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1

# Redis
redis==5.0.1
aioredis==2.0.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Validation and Serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Utilities
python-dateutil==2.8.2
pytz==2023.3
uuid==1.30

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Logging and Monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Environment and Configuration
python-dotenv==1.0.0

# AI/ML (for dream analysis)
openai==1.3.7
transformers==4.35.2
torch==2.1.1

# Background Tasks
celery==5.3.4
flower==2.0.1

# Email
fastapi-mail==1.4.1

# File handling
aiofiles==23.2.1

# CORS
fastapi-cors==0.0.6

# Rate limiting
slowapi==0.1.9

# Encryption
cryptography==41.0.8

# JSON Web Tokens
PyJWT==2.8.0

# Database migrations
yoyo-migrations==8.2.0

# Caching
aiocache==0.12.2

# Validation
email-validator==2.1.0

# Time handling
arrow==1.3.0

# Configuration management
dynaconf==3.2.4
