# FastAPI and ASGI server
fastapi>=0.104.0,<0.110.0
uvicorn[standard]>=0.24.0,<0.30.0

# Database
sqlalchemy>=2.0.20,<2.1.0
asyncpg>=0.29.0,<0.30.0
alembic>=1.12.0,<1.14.0

# Redis
redis>=5.0.0,<6.0.0
aioredis>=2.0.1,<3.0.0

# Authentication and Security
python-jose[cryptography]>=3.3.0,<4.0.0
passlib[bcrypt]>=1.7.4,<2.0.0
python-multipart>=0.0.6,<1.0.0

# Validation and Serialization
pydantic>=2.5.0,<3.0.0
pydantic-settings>=2.1.0,<3.0.0

# HTTP Client
httpx>=0.25.0,<1.0.0
aiohttp>=3.9.0,<4.0.0

# Utilities
python-dateutil>=2.8.0,<3.0.0
pytz>=2023.3

# Development and Testing
pytest>=7.4.0,<8.0.0
pytest-asyncio>=0.21.0,<1.0.0
pytest-cov>=4.1.0,<5.0.0

# Logging and Monitoring
structlog>=23.2.0,<24.0.0
sentry-sdk[fastapi]>=1.38.0,<2.0.0

# Environment and Configuration
python-dotenv>=1.0.0,<2.0.0

# AI/ML (for dream analysis) - Optional
openai>=1.3.0,<2.0.0

# Background Tasks
celery>=5.3.0,<6.0.0
flower>=2.0.0,<3.0.0

# Email
fastapi-mail>=1.4.0,<2.0.0

# File handling
aiofiles>=23.2.0,<24.0.0

# Rate limiting
slowapi>=0.1.9,<1.0.0

# Encryption
cryptography>=41.0.0

# JSON Web Tokens
PyJWT>=2.8.0,<3.0.0

# Caching
aiocache>=0.12.0,<1.0.0

# Validation
email-validator>=2.1.0,<3.0.0

# Time handling
arrow>=1.3.0,<2.0.0
