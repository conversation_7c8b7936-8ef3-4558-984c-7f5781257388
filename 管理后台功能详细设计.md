# LotusDream 管理后台 - 功能详细设计

## 📋 文档说明

本文档详细描述LotusDream管理后台各功能模块的具体设计，包括页面布局、交互流程、数据结构等。

## 🏠 1. 仪表盘模块详细设计

### 1.1 页面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 顶部导航栏：Logo | 用户信息 | 消息通知 | 退出登录              │
├─────────────────────────────────────────────────────────────┤
│ 左侧菜单 │ 主内容区域                                        │
│          │ ┌─────────────────────────────────────────────┐   │
│ • 仪表盘  │ │ 关键指标卡片区域                              │   │
│ • 用户管理│ │ [总用户数] [活跃用户] [梦境记录] [解析完成]    │   │
│ • 梦境数据│ └─────────────────────────────────────────────┘   │
│ • AI解析  │ ┌─────────────────────────────────────────────┐   │
│ • 数据分析│ │ 用户增长趋势图                                │   │
│ • 内容管理│ │ [折线图：最近30天用户增长]                    │   │
│ • 系统管理│ └─────────────────────────────────────────────┘   │
│ • 运营工具│ ┌─────────────────────────────────────────────┐   │
│          │ │ 实时活动监控                                  │   │
│          │ │ [在线用户] [解析队列] [系统状态]              │   │
│          │ └─────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 关键指标卡片
**数据源**：实时统计API
**刷新频率**：每5分钟自动刷新

| 指标名称 | 数据来源 | 计算方式 | 显示格式 |
|----------|----------|----------|----------|
| 总用户数 | users表 | COUNT(user_id) | 数字+增长率 |
| 活跃用户 | 登录日志 | 最近7天登录用户数 | 数字+环比 |
| 梦境记录 | dreams表 | 今日新增记录数 | 数字+同比 |
| 解析完成 | analysis表 | 今日完成解析数 | 数字+成功率 |

### 1.3 用户增长趋势图
**图表类型**：折线图
**时间维度**：最近30天
**数据点**：每日新增用户数、累计用户数
**交互功能**：悬停显示详细数据、时间范围选择

### 1.4 实时监控面板
**更新频率**：实时更新
**监控项目**：
- 在线用户数（WebSocket连接数）
- 解析队列长度（待处理任务数）
- 系统状态（CPU、内存、磁盘使用率）
- 错误告警（最近1小时错误数）

## 👥 2. 用户管理模块详细设计

### 2.1 用户列表页面
**页面功能**：用户信息查询、管理和分析

**表格字段设计**：
| 字段名称 | 数据类型 | 显示宽度 | 排序 | 筛选 |
|----------|----------|----------|------|------|
| 用户ID | string | 120px | ✓ | ✓ |
| 用户名 | string | 150px | ✓ | ✓ |
| 邮箱 | string | 200px | ✓ | ✓ |
| 注册时间 | datetime | 150px | ✓ | ✓ |
| 最后登录 | datetime | 150px | ✓ | ✓ |
| 梦境数量 | number | 100px | ✓ | ✓ |
| 状态 | enum | 100px | ✓ | ✓ |
| 操作 | actions | 120px | - | - |

**筛选条件**：
- 注册时间范围
- 最后登录时间
- 用户状态（正常/禁用/删除）
- 梦境数量范围
- 用户标签

**批量操作**：
- 批量禁用/启用
- 批量发送消息
- 批量导出数据
- 批量添加标签

### 2.2 用户详情页面
**页面布局**：标签页形式

**基本信息标签**：
- 用户基本资料
- 账号状态和权限
- 注册和登录信息
- 设备信息记录

**使用统计标签**：
- 梦境记录统计图表
- 使用频率分析
- 功能使用偏好
- 活跃度趋势

**行为轨迹标签**：
- 操作时间线
- 页面访问记录
- 功能使用路径
- 异常行为标记

**数据管理标签**：
- 梦境记录列表
- 解析结果查看
- 数据导出功能
- 数据删除操作

### 2.3 用户标签管理
**功能描述**：为用户添加自定义标签，便于分类管理

**标签类型**：
- 用户属性标签（年龄段、性别、地区）
- 行为标签（活跃用户、沉默用户、流失用户）
- 业务标签（VIP用户、测试用户、问题用户）
- 自定义标签（运营人员自定义）

**标签操作**：
- 创建新标签
- 编辑标签信息
- 删除标签
- 批量添加/移除标签

## 🌙 3. 梦境数据管理模块详细设计

### 3.1 梦境记录列表
**页面功能**：梦境记录查询、审核和管理

**列表字段**：
| 字段名称 | 显示内容 | 操作功能 |
|----------|----------|----------|
| 记录ID | dream_id | 复制ID |
| 用户信息 | 用户名+ID | 查看用户详情 |
| 记录时间 | 梦境日期 | 时间排序 |
| 情绪标签 | 情绪列表 | 按情绪筛选 |
| 内容预览 | 前50字符 | 查看完整内容 |
| 解析状态 | 状态标识 | 状态筛选 |
| 质量评分 | 0-100分 | 质量排序 |
| 操作 | 查看/编辑/删除 | 权限控制 |

**搜索功能**：
- 关键词搜索（梦境内容）
- 用户ID/用户名搜索
- 时间范围筛选
- 情绪标签筛选
- 解析状态筛选
- 质量评分范围

### 3.2 梦境内容详情
**页面布局**：弹窗形式

**内容展示**：
- 梦境完整描述
- 情绪标签显示
- 记录时间和时区
- 用户基本信息

**解析结果展示**：
- 关键词提取结果
- 心理暗示分析
- 象征意义解读
- 行动建议列表
- 诗意句子匹配
- 置信度评分

**操作功能**：
- 内容编辑（敏感内容处理）
- 解析结果修正
- 质量评分调整
- 标记为优质内容
- 添加管理员备注

### 3.3 敏感内容监控
**监控机制**：
- 关键词自动检测
- AI内容审核
- 人工复审流程
- 用户举报处理

**处理流程**：
1. 自动检测标记
2. 人工审核确认
3. 内容处理决策
4. 用户通知反馈
5. 记录处理日志

## 🧠 4. AI解析管理模块详细设计

### 4.1 解析任务监控
**实时监控面板**：
- 任务队列长度
- 处理中任务数
- 平均处理时间
- 成功/失败率统计

**任务列表**：
| 字段 | 内容 | 状态 |
|------|------|------|
| 任务ID | analysis_id | 可点击查看详情 |
| 梦境ID | dream_id | 关联梦境记录 |
| 提交时间 | 任务创建时间 | 时间排序 |
| 处理状态 | 队列中/处理中/完成/失败 | 状态筛选 |
| 处理时长 | 实际耗时 | 性能分析 |
| 错误信息 | 失败原因 | 错误分类 |

### 4.2 解析质量管理
**质量评估指标**：
- 关键词准确性
- 分析内容相关性
- 建议实用性评分
- 用户满意度反馈

**质量监控**：
- 质量趋势图表
- 异常质量告警
- 质量分布统计
- 改进建议生成

### 4.3 解析模板管理
**模板类型**：
- 心理暗示分析模板
- 象征意义解读模板
- 行动建议模板
- 诗意句子模板

**模板操作**：
- 新增模板
- 编辑模板内容
- 模板版本管理
- 模板效果评估
- 模板启用/禁用

## 📊 5. 数据分析模块详细设计

### 5.1 用户行为分析
**分析维度**：
- 用户活跃度分析
- 功能使用偏好
- 用户留存分析
- 用户流失分析

**图表类型**：
- 漏斗图：用户转化流程
- 热力图：功能使用热度
- 留存曲线：用户留存趋势
- 分布图：用户属性分布

### 5.2 产品使用统计
**统计指标**：
- 梦境记录频次分布
- 解析功能使用率
- 情绪标签使用统计
- 功能路径分析

**报表功能**：
- 自定义时间范围
- 多维度数据钻取
- 数据导出功能
- 定时报表生成

### 5.3 趋势预测分析
**预测模型**：
- 用户增长预测
- 活跃度趋势预测
- 功能使用趋势
- 业务指标预测

**预测展示**：
- 趋势预测曲线
- 置信区间显示
- 影响因子分析
- 预测准确度评估

## 🎨 6. 内容管理模块详细设计

### 6.1 诗意句子库管理
**数据结构**：
```json
{
  "id": "poem_001",
  "content": "莲花不染淤泥，心境自然清明",
  "description": "愿你如莲花般，在浮世中保持内心的纯净",
  "category": "nature",
  "keywords": ["莲花", "纯净", "宁静"],
  "emotion_tags": ["平静", "安详"],
  "usage_count": 156,
  "rating": 4.8,
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

**管理功能**：
- 句子库CRUD操作
- 分类标签管理
- 使用统计分析
- 质量评分系统
- 批量导入导出

### 6.2 情绪标签配置
**标签管理**：
- 情绪标签列表
- 标签分类管理
- 标签使用统计
- 标签关联分析

**配置功能**：
- 新增情绪标签
- 编辑标签属性
- 设置标签权重
- 标签启用/禁用

### 6.3 帮助文档管理
**文档结构**：
- 文档分类目录
- 富文本编辑器
- 版本历史管理
- 多语言支持

**发布流程**：
- 草稿编辑
- 内容审核
- 发布上线
- 更新维护

## ⚙️ 7. 系统管理模块详细设计

### 7.1 管理员账号管理
**账号信息**：
- 基本信息（用户名、邮箱、姓名）
- 角色权限分配
- 登录状态管理
- 操作日志记录

**安全设置**：
- 密码策略配置
- 双因子认证
- 登录IP限制
- 会话超时设置

### 7.2 角色权限配置
**权限模型**：RBAC（基于角色的访问控制）

**权限类型**：
- 菜单访问权限
- 功能操作权限
- 数据查看权限
- 数据修改权限

**权限矩阵**：
| 角色 | 用户管理 | 数据查看 | 数据修改 | 系统配置 |
|------|----------|----------|----------|----------|
| 超级管理员 | ✓ | ✓ | ✓ | ✓ |
| 产品运营 | ✓ | ✓ | ✓ | ✗ |
| 客服人员 | 查看 | ✓ | 部分 | ✗ |
| 数据分析师 | 查看 | ✓ | ✗ | ✗ |

### 7.3 操作日志管理
**日志记录**：
- 用户操作日志
- 系统事件日志
- 错误异常日志
- 安全审计日志

**日志查询**：
- 时间范围筛选
- 操作类型筛选
- 用户筛选
- 关键词搜索

## 🛠️ 8. 运营工具模块详细设计

### 8.1 消息推送管理
**推送类型**：
- 系统通知
- 运营活动
- 产品更新
- 个性化推荐

**推送渠道**：
- APP内推送
- 邮件推送
- 短信推送（可选）

**推送管理**：
- 消息编辑器
- 目标用户选择
- 发送时间设置
- 效果统计分析

### 8.2 用户反馈处理
**反馈分类**：
- 功能建议
- 问题报告
- 投诉建议
- 其他反馈

**处理流程**：
1. 反馈收集
2. 分类标记
3. 分配处理人
4. 问题解决
5. 用户回复
6. 满意度调查

### 8.3 工单系统
**工单类型**：
- 技术支持
- 账号问题
- 数据问题
- 功能咨询

**工单状态**：
- 待处理
- 处理中
- 待用户确认
- 已解决
- 已关闭

**处理流程**：
- 工单创建
- 自动分配
- 处理跟进
- 状态更新
- 结果反馈

## 📱 9. 响应式设计规范

### 9.1 断点设计
- **大屏幕**：≥1200px（桌面显示器）
- **中等屏幕**：992px-1199px（小桌面）
- **小屏幕**：768px-991px（平板）
- **超小屏幕**：<768px（手机）

### 9.2 布局适配
**桌面端**：
- 左侧固定菜单 + 主内容区
- 表格完整显示
- 多列布局

**平板端**：
- 可收缩侧边菜单
- 表格横向滚动
- 两列布局

**手机端**：
- 底部导航菜单
- 卡片式布局
- 单列显示

### 9.3 交互适配
- 触摸友好的按钮尺寸
- 手势操作支持
- 简化的操作流程
- 优化的表单设计

## 🔧 10. 技术实现要点

### 10.1 前端架构
**技术栈**：React + TypeScript + Ant Design
**状态管理**：Redux Toolkit
**路由管理**：React Router
**HTTP客户端**：Axios
**图表库**：ECharts

### 10.2 性能优化
- 组件懒加载
- 虚拟滚动
- 数据缓存
- 图片懒加载
- 代码分割

### 10.3 安全措施
- XSS防护
- CSRF防护
- 权限验证
- 数据加密
- 安全审计

## 📞 联系信息

**产品经理**：<EMAIL>  
**技术负责人**：<EMAIL>  
**设计负责人**：<EMAIL>  
**项目支持**：<EMAIL>
