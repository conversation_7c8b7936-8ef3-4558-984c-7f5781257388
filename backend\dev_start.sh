#!/bin/bash

# LotusDream 开发环境快速启动脚本

echo "🚀 LotusDream 开发环境启动..."

# 检查是否已安装依赖
if ! command -v uvicorn &> /dev/null; then
    echo "📦 uvicorn 未找到，安装依赖..."

    # 检查是否有虚拟环境
    if [ -d "venv" ]; then
        echo "🔄 激活虚拟环境..."
        source venv/bin/activate 2>/dev/null || venv\Scripts\activate 2>/dev/null || echo "请手动激活虚拟环境"
    else
        echo "💡 建议创建虚拟环境以避免依赖冲突"
        read -p "是否创建虚拟环境？(y/n): " create_venv
        if [ "$create_venv" = "y" ]; then
            python -m venv venv
            source venv/bin/activate 2>/dev/null || venv\Scripts\activate 2>/dev/null
            echo "✅ 虚拟环境已创建并激活"
        fi
    fi

    # 安装依赖
    echo "📦 安装 Python 依赖..."
    pip install -r requirements-minimal.txt
    echo "✅ 依赖安装完成"
fi

# 启动数据库服务（如果未运行）
if ! docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
    echo "🗄️  启动数据库服务..."
    docker-compose -f docker-compose.dev.yml up -d

    echo "⏳ 等待数据库启动..."
    sleep 10

    # 检查数据库连接
    until docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres > /dev/null 2>&1; do
        echo "等待 PostgreSQL..."
        sleep 2
    done
    echo "✅ 数据库已就绪"
else
    echo "✅ 数据库服务已运行"
fi

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "⚠️  .env 文件不存在，复制示例文件..."
    cp .env.example .env
fi

# 运行数据库迁移（如果需要）
echo "🔄 检查数据库迁移..."
# python -m alembic upgrade head  # 如果有迁移文件

echo "🌟 启动 API 服务..."
echo "📍 API 地址: http://localhost:8000"
echo "📚 API 文档: http://localhost:8000/docs"
echo "🔍 健康检查: http://localhost:8000/health"
echo ""
echo "按 Ctrl+C 停止服务"
echo "=========================="

# 启动 API（自动重载）
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
