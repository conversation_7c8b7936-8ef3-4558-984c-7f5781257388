@echo off
REM LotusDream 开发环境启动脚本 (Windows)

echo 🚀 LotusDream 开发环境启动...

REM 检查是否已安装依赖
where uvicorn >nul 2>nul
if %errorlevel% neq 0 (
    echo 📦 uvicorn 未找到，安装依赖...
    
    REM 检查是否有虚拟环境
    if exist "venv" (
        echo 🔄 激活虚拟环境...
        call venv\Scripts\activate
    ) else (
        echo 💡 建议创建虚拟环境以避免依赖冲突
        set /p create_venv="是否创建虚拟环境？(y/n): "
        if /i "%create_venv%"=="y" (
            python -m venv venv
            call venv\Scripts\activate
            echo ✅ 虚拟环境已创建并激活
        )
    )
    
    REM 安装依赖
    echo 📦 安装 Python 依赖...
    pip install -r requirements-minimal.txt
    echo ✅ 依赖安装完成
)

REM 启动数据库服务
echo 🗄️  检查数据库服务...
docker-compose -f docker-compose.dev.yml ps | findstr "Up" >nul
if %errorlevel% neq 0 (
    echo 🗄️  启动数据库服务...
    docker-compose -f docker-compose.dev.yml up -d
    
    echo ⏳ 等待数据库启动...
    timeout /t 10 /nobreak >nul
    echo ✅ 数据库已就绪
) else (
    echo ✅ 数据库服务已运行
)

REM 检查环境变量
if not exist ".env" (
    echo ⚠️  .env 文件不存在，复制示例文件...
    copy .env.example .env
)

echo 🌟 启动 API 服务...
echo 📍 API 地址: http://localhost:8000
echo 📚 API 文档: http://localhost:8000/docs
echo 🔍 健康检查: http://localhost:8000/health
echo.
echo 按 Ctrl+C 停止服务
echo ==========================

REM 启动 API
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
