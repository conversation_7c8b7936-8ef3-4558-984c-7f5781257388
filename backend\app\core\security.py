"""
Security utilities for authentication and authorization
"""

from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any
from passlib.context import Crypt<PERSON>ontext
from jose import JW<PERSON>rror, jwt
from fastapi import HTTPException, status
import secrets
import structlog

from app.core.config import settings

logger = structlog.get_logger()

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class SecurityUtils:
    """Security utility functions"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_random_string(length: int = 32) -> str:
        """Generate a random string"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """Validate password strength"""
        errors = []
        
        if len(password) < settings.PASSWORD_MIN_LENGTH:
            errors.append(f"Password must be at least {settings.PASSWORD_MIN_LENGTH} characters long")
        
        if len(password) > settings.PASSWORD_MAX_LENGTH:
            errors.append(f"Password must be no more than {settings.PASSWORD_MAX_LENGTH} characters long")
        
        if not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one digit")
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors
        }


class JWTManager:
    """JWT token management"""
    
    @staticmethod
    def create_access_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        to_encode.update({"exp": expire, "type": "access"})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create refresh token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=settings.REFRESH_TOKEN_EXPIRE_DAYS
            )
        
        to_encode.update({"exp": expire, "type": "refresh"})
        
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode token"""
        try:
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )
            
            # Check token type
            if payload.get("type") != token_type:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token type"
                )
            
            # Check expiration
            exp = payload.get("exp")
            if exp is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token missing expiration"
                )
            
            if datetime.utcnow() > datetime.fromtimestamp(exp):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token expired"
                )
            
            return payload
            
        except JWTError as e:
            logger.error("JWT verification error", error=str(e))
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials"
            )
    
    @staticmethod
    def get_user_id_from_token(token: str) -> str:
        """Extract user ID from token"""
        payload = JWTManager.verify_token(token)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token missing user ID"
            )
        
        return user_id


class PermissionChecker:
    """Permission checking utilities"""
    
    # Define permission levels
    PERMISSIONS = {
        "user:read": "Read user information",
        "user:write": "Modify user information",
        "user:delete": "Delete user accounts",
        "dream:read": "Read dream records",
        "dream:write": "Modify dream records",
        "dream:delete": "Delete dream records",
        "analysis:read": "Read analysis results",
        "analysis:write": "Modify analysis results",
        "analysis:execute": "Execute dream analysis",
        "statistics:read": "Read statistics data",
        "admin:read": "Read admin data",
        "admin:write": "Modify admin settings",
        "system:config": "Configure system settings"
    }
    
    @staticmethod
    def check_permission(user_permissions: list, required_permission: str) -> bool:
        """Check if user has required permission"""
        return required_permission in user_permissions
    
    @staticmethod
    def check_multiple_permissions(
        user_permissions: list,
        required_permissions: list,
        require_all: bool = True
    ) -> bool:
        """Check multiple permissions"""
        if require_all:
            return all(perm in user_permissions for perm in required_permissions)
        else:
            return any(perm in user_permissions for perm in required_permissions)


# Create utility instances
security = SecurityUtils()
jwt_manager = JWTManager()
permission_checker = PermissionChecker()
