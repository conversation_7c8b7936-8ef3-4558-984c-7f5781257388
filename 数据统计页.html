<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LotusDream - 数据统计</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar {
            display: none;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: black;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        .header {
            height: 60px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .time-selector {
            height: 50px;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .content-area {
            height: calc(100% - 44px - 60px - 50px - 83px);
            padding: 20px;
            overflow-y: auto;
        }
        .content-area::-webkit-scrollbar {
            display: none;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 12px;
        }
        .nav-item.active {
            color: #667eea;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }
        .time-btn {
            background: white;
            border: 1px solid #e5e7eb;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            margin: 0 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .time-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }
        .chart-placeholder {
            height: 120px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 14px;
            margin: 12px 0;
        }
        .progress-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .keyword-cloud {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 12px 0;
        }
        .keyword-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0.8;
        }
        .keyword-item.large {
            font-size: 14px;
            opacity: 1;
        }
        .keyword-item.medium {
            font-size: 13px;
            opacity: 0.9;
        }
        .health-score {
            text-align: center;
            padding: 20px;
        }
        .score-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: conic-gradient(from 0deg, #667eea 0deg, #764ba2 252deg, #e5e7eb 252deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            position: relative;
        }
        .score-inner {
            width: 60px;
            height: 60px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup mx-auto">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 头部导航 -->
            <div class="header">
                <h2 class="text-lg font-semibold text-gray-800">数据统计</h2>
                <i class="fas fa-download text-gray-600"></i>
            </div>

            <!-- 时间选择器 -->
            <div class="time-selector">
                <button class="time-btn">周</button>
                <button class="time-btn active">月</button>
                <button class="time-btn">年</button>
            </div>

            <!-- 主要内容区域 -->
            <div class="content-area">
                <!-- 心理健康指数 -->
                <div class="stat-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-heart text-red-500 mr-2"></i>心理健康指数
                    </h3>
                    <div class="health-score">
                        <div class="score-circle">
                            <div class="score-inner">78</div>
                        </div>
                        <p class="text-sm text-gray-600">本月平均分</p>
                        <p class="text-xs text-green-600 mt-1">↑ 比上月提升 5%</p>
                    </div>
                </div>

                <!-- 梦境频率统计 -->
                <div class="stat-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-moon text-indigo-500 mr-2"></i>梦境记录频率
                    </h3>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-bar text-2xl mr-2"></i>
                        柱状图：本月记录 15 次
                    </div>
                    <div class="flex justify-between text-xs text-gray-500">
                        <span>平均每周 3.8 次</span>
                        <span>最高连续 7 天</span>
                    </div>
                </div>

                <!-- 情绪趋势 -->
                <div class="stat-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-smile text-yellow-500 mr-2"></i>情绪趋势分析
                    </h3>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line text-2xl mr-2"></i>
                        曲线图：情绪变化趋势
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">积极情绪</span>
                            <span class="text-xs text-green-600">65%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 65%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">中性情绪</span>
                            <span class="text-xs text-gray-600">25%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 25%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-600">消极情绪</span>
                            <span class="text-xs text-red-600">10%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 10%"></div>
                        </div>
                    </div>
                </div>

                <!-- 关键词云图 -->
                <div class="stat-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-cloud text-blue-500 mr-2"></i>梦境关键词
                    </h3>
                    <div class="keyword-cloud">
                        <span class="keyword-item large">莲花</span>
                        <span class="keyword-item medium">飞翔</span>
                        <span class="keyword-item">花园</span>
                        <span class="keyword-item medium">家人</span>
                        <span class="keyword-item">水</span>
                        <span class="keyword-item large">自由</span>
                        <span class="keyword-item">迷宫</span>
                        <span class="keyword-item medium">考试</span>
                        <span class="keyword-item">阳光</span>
                        <span class="keyword-item">音乐</span>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">字体大小反映出现频率</p>
                </div>

                <!-- 统计摘要 -->
                <div class="stat-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-list text-purple-500 mr-2"></i>本月统计摘要
                    </h3>
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-purple-600">15</div>
                            <div class="text-xs text-gray-600">梦境记录</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-blue-600">12</div>
                            <div class="text-xs text-gray-600">解析完成</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-green-600">7</div>
                            <div class="text-xs text-gray-600">连续天数</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-orange-600">28</div>
                            <div class="text-xs text-gray-600">使用天数</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>解析</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-history"></i>
                    <span>历史</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-chart-line"></i>
                    <span>统计</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
