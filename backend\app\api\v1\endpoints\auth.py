"""
Authentication endpoints
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTP<PERSON>x<PERSON>, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import timedelta
import structlog

from app.core.database import get_db
from app.core.security import security, jwt_manager
from app.core.exceptions import (
    ValidationException,
    AuthenticationException,
    ConflictException,
    InvalidCredentialsException,
    EmailAlreadyExistsException,
    UsernameAlreadyExistsException
)
from app.schemas.user import (
    UserCreate,
    UserLogin,
    TokenResponse,
    RefreshTokenRequest,
    UserResponse
)
from app.services.user_service import UserService
from app.services.auth_service import AuthService

logger = structlog.get_logger()
router = APIRouter()
security_scheme = HTTPBearer()


@router.post("/register", response_model=TokenResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserCreate,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user
    """
    try:
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        # Check if email already exists
        existing_user = await user_service.get_user_by_email(user_data.email)
        if existing_user:
            raise EmailAlreadyExistsException(user_data.email)
        
        # Check if username already exists
        existing_user = await user_service.get_user_by_username(user_data.username)
        if existing_user:
            raise UsernameAlreadyExistsException(user_data.username)
        
        # Create user
        user = await user_service.create_user(
            user_data=user_data,
            registration_ip=request.client.host
        )
        
        # Generate tokens
        access_token = jwt_manager.create_access_token(
            data={"sub": user.user_id, "username": user.username}
        )
        
        refresh_token = jwt_manager.create_refresh_token(
            data={"sub": user.user_id}
        )
        
        # Create session
        await auth_service.create_session(
            user_id=user.user_id,
            refresh_token=refresh_token,
            device_info=user_data.device_id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        logger.info("User registered successfully", user_id=user.user_id, email=user.email)
        
        return TokenResponse(
            user_id=user.user_id,
            username=user.username,
            token=access_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except (EmailAlreadyExistsException, UsernameAlreadyExistsException) as e:
        raise e
    except Exception as e:
        logger.error("Registration failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    user_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    User login
    """
    try:
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        # Authenticate user
        user = await auth_service.authenticate_user(
            email=user_data.email,
            password=user_data.password
        )
        
        if not user:
            # Log failed login attempt
            await auth_service.log_login_attempt(
                email=user_data.email,
                success=False,
                failure_reason="Invalid credentials",
                ip_address=request.client.host,
                user_agent=request.headers.get("user-agent")
            )
            raise InvalidCredentialsException()
        
        # Generate tokens
        access_token = jwt_manager.create_access_token(
            data={"sub": user.user_id, "username": user.username}
        )
        
        refresh_token = jwt_manager.create_refresh_token(
            data={"sub": user.user_id}
        )
        
        # Create session
        await auth_service.create_session(
            user_id=user.user_id,
            refresh_token=refresh_token,
            device_info=user_data.device_id,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        # Update last login
        await user_service.update_last_login(user.user_id)
        
        # Log successful login
        await auth_service.log_login_attempt(
            user_id=user.user_id,
            email=user_data.email,
            success=True,
            ip_address=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        logger.info("User logged in successfully", user_id=user.user_id, email=user.email)
        
        return TokenResponse(
            user_id=user.user_id,
            username=user.username,
            token=access_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            last_login=user.last_login
        )
        
    except InvalidCredentialsException as e:
        raise e
    except Exception as e:
        logger.error("Login failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token
    """
    try:
        auth_service = AuthService(db)
        user_service = UserService(db)
        
        # Verify refresh token
        payload = jwt_manager.verify_token(refresh_data.refresh_token, "refresh")
        user_id = payload.get("sub")
        
        # Validate session
        session = await auth_service.get_valid_session(user_id, refresh_data.refresh_token)
        if not session:
            raise AuthenticationException("Invalid refresh token")
        
        # Get user
        user = await user_service.get_user_by_id(user_id)
        if not user or not user.is_authenticated:
            raise AuthenticationException("User not found or inactive")
        
        # Generate new access token
        access_token = jwt_manager.create_access_token(
            data={"sub": user.user_id, "username": user.username}
        )
        
        # Update session last used
        await auth_service.update_session_last_used(session.session_id)
        
        logger.info("Token refreshed successfully", user_id=user.user_id)
        
        return TokenResponse(
            user_id=user.user_id,
            username=user.username,
            token=access_token,
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except AuthenticationException as e:
        raise e
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: AsyncSession = Depends(get_db)
):
    """
    User logout
    """
    try:
        auth_service = AuthService(db)
        
        # Verify token
        payload = jwt_manager.verify_token(credentials.credentials)
        user_id = payload.get("sub")
        
        # Invalidate all user sessions
        await auth_service.invalidate_user_sessions(user_id)
        
        logger.info("User logged out successfully", user_id=user_id)
        
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        logger.error("Logout failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )
