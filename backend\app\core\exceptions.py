"""
Custom exceptions for the application
"""

from typing import Optional, List, Dict, Any
from fastapi import HTTPException, status


class APIException(HTTPException):
    """Base API exception class"""
    
    def __init__(
        self,
        status_code: int,
        message: str,
        error_type: str = "API_ERROR",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        self.status_code = status_code
        self.message = message
        self.error_type = error_type
        self.details = details or []
        super().__init__(status_code=status_code, detail=message)


class ValidationException(APIException):
    """Validation error exception"""
    
    def __init__(
        self,
        message: str = "Validation error",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            message=message,
            error_type="VALIDATION_ERROR",
            details=details
        )


class AuthenticationException(APIException):
    """Authentication error exception"""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            message=message,
            error_type="AUTHENTICATION_ERROR",
            details=details
        )


class AuthorizationException(APIException):
    """Authorization error exception"""
    
    def __init__(
        self,
        message: str = "Insufficient permissions",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            message=message,
            error_type="AUTHORIZATION_ERROR",
            details=details
        )


class NotFoundException(APIException):
    """Resource not found exception"""
    
    def __init__(
        self,
        message: str = "Resource not found",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            message=message,
            error_type="NOT_FOUND_ERROR",
            details=details
        )


class ConflictException(APIException):
    """Resource conflict exception"""
    
    def __init__(
        self,
        message: str = "Resource conflict",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            message=message,
            error_type="CONFLICT_ERROR",
            details=details
        )


class RateLimitException(APIException):
    """Rate limit exceeded exception"""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            message=message,
            error_type="RATE_LIMIT_ERROR",
            details=details
        )


class InternalServerException(APIException):
    """Internal server error exception"""
    
    def __init__(
        self,
        message: str = "Internal server error",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            message=message,
            error_type="INTERNAL_SERVER_ERROR",
            details=details
        )


class ServiceUnavailableException(APIException):
    """Service unavailable exception"""
    
    def __init__(
        self,
        message: str = "Service temporarily unavailable",
        details: Optional[List[Dict[str, Any]]] = None
    ):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            message=message,
            error_type="SERVICE_UNAVAILABLE_ERROR",
            details=details
        )


# Specific business logic exceptions

class UserNotFoundException(NotFoundException):
    """User not found exception"""
    
    def __init__(self, user_id: str):
        super().__init__(
            message=f"User with ID {user_id} not found",
            details=[{"field": "user_id", "value": user_id}]
        )


class DreamNotFoundException(NotFoundException):
    """Dream record not found exception"""
    
    def __init__(self, dream_id: str):
        super().__init__(
            message=f"Dream record with ID {dream_id} not found",
            details=[{"field": "dream_id", "value": dream_id}]
        )


class AnalysisNotFoundException(NotFoundException):
    """Analysis not found exception"""
    
    def __init__(self, analysis_id: str):
        super().__init__(
            message=f"Analysis with ID {analysis_id} not found",
            details=[{"field": "analysis_id", "value": analysis_id}]
        )


class EmailAlreadyExistsException(ConflictException):
    """Email already exists exception"""
    
    def __init__(self, email: str):
        super().__init__(
            message=f"Email {email} is already registered",
            details=[{"field": "email", "value": email}]
        )


class UsernameAlreadyExistsException(ConflictException):
    """Username already exists exception"""
    
    def __init__(self, username: str):
        super().__init__(
            message=f"Username {username} is already taken",
            details=[{"field": "username", "value": username}]
        )


class InvalidCredentialsException(AuthenticationException):
    """Invalid credentials exception"""
    
    def __init__(self):
        super().__init__(
            message="Invalid email or password",
            details=[{"field": "credentials", "message": "Email or password is incorrect"}]
        )


class AccountDisabledException(AuthenticationException):
    """Account disabled exception"""
    
    def __init__(self):
        super().__init__(
            message="Account is disabled",
            details=[{"field": "account", "message": "This account has been disabled"}]
        )


class AnalysisInProgressException(ConflictException):
    """Analysis already in progress exception"""
    
    def __init__(self, dream_id: str):
        super().__init__(
            message=f"Analysis for dream {dream_id} is already in progress",
            details=[{"field": "dream_id", "value": dream_id}]
        )


class AnalysisFailedException(InternalServerException):
    """Analysis failed exception"""
    
    def __init__(self, dream_id: str, reason: str):
        super().__init__(
            message=f"Analysis failed for dream {dream_id}: {reason}",
            details=[
                {"field": "dream_id", "value": dream_id},
                {"field": "reason", "value": reason}
            ]
        )


class InvalidTokenException(AuthenticationException):
    """Invalid token exception"""
    
    def __init__(self, token_type: str = "access"):
        super().__init__(
            message=f"Invalid {token_type} token",
            details=[{"field": "token", "type": token_type}]
        )


class TokenExpiredException(AuthenticationException):
    """Token expired exception"""
    
    def __init__(self, token_type: str = "access"):
        super().__init__(
            message=f"{token_type.title()} token has expired",
            details=[{"field": "token", "type": token_type}]
        )


class InsufficientPermissionsException(AuthorizationException):
    """Insufficient permissions exception"""
    
    def __init__(self, required_permission: str):
        super().__init__(
            message=f"Insufficient permissions. Required: {required_permission}",
            details=[{"field": "permission", "required": required_permission}]
        )
