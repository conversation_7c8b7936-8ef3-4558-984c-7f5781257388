<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LotusDream - 梦境解析</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar {
            display: none;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: black;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        .header {
            height: 60px;
            background: transparent;
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .content-area {
            height: calc(100% - 44px - 60px - 83px);
            padding: 20px;
            overflow-y: auto;
        }
        .content-area::-webkit-scrollbar {
            display: none;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 12px;
        }
        .nav-item.active {
            color: #667eea;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .keyword-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin: 4px;
            display: inline-block;
        }
        .analysis-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }
        .poetry-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .action-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            border: none;
            cursor: pointer;
            margin: 8px;
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .suggestion-item {
            background: #f8fafc;
            border-left: 4px solid #667eea;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup mx-auto">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 头部导航 -->
            <div class="header">
                <i class="fas fa-arrow-left text-gray-700 text-lg mr-4"></i>
                <h2 class="text-lg font-semibold text-gray-800">梦境解析结果</h2>
            </div>

            <!-- 主要内容区域 -->
            <div class="content-area">
                <!-- 关键词标签 -->
                <div class="analysis-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">梦境关键词</h3>
                    <div>
                        <span class="keyword-tag">莲花</span>
                        <span class="keyword-tag">花园</span>
                        <span class="keyword-tag">宁静</span>
                    </div>
                </div>

                <!-- 心理暗示分析 -->
                <div class="analysis-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-brain text-purple-500 mr-2"></i>心理暗示
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        莲花在梦境中通常象征着内心的纯净与成长。你的潜意识正在寻求内心的平静与和谐，这可能反映了你对精神层面提升的渴望。
                    </p>
                </div>

                <!-- 象征意义 -->
                <div class="analysis-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-eye text-indigo-500 mr-2"></i>象征意义
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        花园代表你的内心世界，而盛开的莲花象征着你正在经历的个人成长和精神觉醒。这个梦境暗示你正处于一个积极的转变期。
                    </p>
                </div>

                <!-- 行动建议 -->
                <div class="analysis-card">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>行动建议
                    </h3>
                    <div class="suggestion-item">
                        <p class="text-sm text-gray-700">尝试冥想或瑜伽，培养内心的平静</p>
                    </div>
                    <div class="suggestion-item">
                        <p class="text-sm text-gray-700">多接触大自然，感受生命的美好</p>
                    </div>
                    <div class="suggestion-item">
                        <p class="text-sm text-gray-700">记录每日感悟，观察内心的变化</p>
                    </div>
                </div>

                <!-- 诗意句子 -->
                <div class="poetry-card">
                    <i class="fas fa-quote-left text-2xl mb-3 opacity-70"></i>
                    <p class="text-lg font-light italic mb-2">
                        "莲花不染淤泥，心境自然清明"
                    </p>
                    <p class="text-sm opacity-80">愿你如莲花般，在浮世中保持内心的纯净</p>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-center space-x-4 mt-6">
                    <button class="action-btn">
                        <i class="fas fa-save mr-2"></i>保存记录
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-home mr-2"></i>返回首页
                    </button>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-brain"></i>
                    <span>解析</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-history"></i>
                    <span>历史</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>统计</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
