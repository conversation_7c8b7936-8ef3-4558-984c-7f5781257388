"""
Redis Configuration and Connection Management
"""

import aioredis
from typing import Optional
import structlog
import json
from datetime import timed<PERSON><PERSON>

from app.core.config import settings

logger = structlog.get_logger()

# Global Redis connection
redis_client: Optional[aioredis.Redis] = None


async def init_redis() -> None:
    """Initialize Redis connection"""
    global redis_client
    
    try:
        redis_client = aioredis.from_url(
            settings.REDIS_URL,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            encoding="utf-8",
            decode_responses=True,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30
        )
        
        # Test connection
        await redis_client.ping()
        logger.info("Redis connection established successfully")
        
    except Exception as e:
        logger.error("Failed to connect to Redis", error=str(e))
        raise


async def close_redis() -> None:
    """Close Redis connection"""
    global redis_client
    
    if redis_client:
        await redis_client.close()
        logger.info("Redis connection closed")


def get_redis() -> aioredis.Redis:
    """Get Redis client instance"""
    if redis_client is None:
        raise RuntimeError("Redis client not initialized")
    return redis_client


class RedisCache:
    """Redis cache utility class"""
    
    def __init__(self):
        self.client = get_redis()
    
    async def get(self, key: str) -> Optional[dict]:
        """Get value from cache"""
        try:
            value = await self.client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.error("Redis get error", key=key, error=str(e))
            return None
    
    async def set(
        self, 
        key: str, 
        value: dict, 
        ttl: Optional[int] = None
    ) -> bool:
        """Set value in cache"""
        try:
            serialized_value = json.dumps(value, default=str)
            if ttl:
                await self.client.setex(key, ttl, serialized_value)
            else:
                await self.client.set(key, serialized_value)
            return True
        except Exception as e:
            logger.error("Redis set error", key=key, error=str(e))
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            result = await self.client.delete(key)
            return bool(result)
        except Exception as e:
            logger.error("Redis delete error", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            result = await self.client.exists(key)
            return bool(result)
        except Exception as e:
            logger.error("Redis exists error", key=key, error=str(e))
            return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for key"""
        try:
            result = await self.client.expire(key, ttl)
            return bool(result)
        except Exception as e:
            logger.error("Redis expire error", key=key, error=str(e))
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment value by amount"""
        try:
            result = await self.client.incrby(key, amount)
            return result
        except Exception as e:
            logger.error("Redis increment error", key=key, error=str(e))
            return None
    
    async def get_pattern(self, pattern: str) -> list:
        """Get all keys matching pattern"""
        try:
            keys = await self.client.keys(pattern)
            return keys
        except Exception as e:
            logger.error("Redis get_pattern error", pattern=pattern, error=str(e))
            return []
    
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        try:
            keys = await self.client.keys(pattern)
            if keys:
                result = await self.client.delete(*keys)
                return result
            return 0
        except Exception as e:
            logger.error("Redis delete_pattern error", pattern=pattern, error=str(e))
            return 0


# Cache key generators
class CacheKeys:
    """Cache key generators"""
    
    @staticmethod
    def user_info(user_id: str) -> str:
        return f"user:info:{user_id}"
    
    @staticmethod
    def user_statistics(user_id: str, period: str) -> str:
        return f"user:stats:{user_id}:{period}"
    
    @staticmethod
    def dream_analysis(dream_id: str) -> str:
        return f"dream:analysis:{dream_id}"
    
    @staticmethod
    def analysis_task(task_id: str) -> str:
        return f"analysis:task:{task_id}"
    
    @staticmethod
    def rate_limit(identifier: str) -> str:
        return f"rate_limit:{identifier}"
    
    @staticmethod
    def session(session_id: str) -> str:
        return f"session:{session_id}"
    
    @staticmethod
    def email_verification(email: str) -> str:
        return f"email:verify:{email}"
    
    @staticmethod
    def password_reset(email: str) -> str:
        return f"password:reset:{email}"


# Create cache instance
cache = RedisCache()
