"""
API Dependencies
"""

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import structlog

from app.core.database import get_db
from app.core.security import jwt_manager
from app.core.exceptions import AuthenticationException, AuthorizationException
from app.models.user import User
from app.services.user_service import UserService

logger = structlog.get_logger()
security_scheme = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security_scheme),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user
    """
    try:
        # Verify token
        payload = jwt_manager.verify_token(credentials.credentials)
        user_id = payload.get("sub")
        
        if user_id is None:
            raise AuthenticationException("Invalid token")
        
        # Get user from database
        user_service = UserService(db)
        user = await user_service.get_user_by_id(user_id)
        
        if user is None:
            raise AuthenticationException("User not found")
        
        if not user.is_authenticated:
            raise AuthenticationException("User account is inactive")
        
        return user
        
    except AuthenticationException as e:
        raise e
    except Exception as e:
        logger.error("Authentication failed", error=str(e))
        raise AuthenticationException("Authentication failed")


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user (alias for get_current_user)
    """
    return current_user


async def get_optional_current_user(
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Optional[User]:
    """
    Get current user if authenticated, otherwise return None
    """
    try:
        # Try to get authorization header
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            return None
        
        token = authorization.split(" ")[1]
        
        # Verify token
        payload = jwt_manager.verify_token(token)
        user_id = payload.get("sub")
        
        if user_id is None:
            return None
        
        # Get user from database
        user_service = UserService(db)
        user = await user_service.get_user_by_id(user_id)
        
        if user is None or not user.is_authenticated:
            return None
        
        return user
        
    except Exception:
        return None


class PermissionChecker:
    """Permission checker dependency"""
    
    def __init__(self, required_permission: str):
        self.required_permission = required_permission
    
    def __call__(self, current_user: User = Depends(get_current_user)) -> User:
        """
        Check if user has required permission
        """
        # For now, we'll implement basic permission checking
        # In a real application, you would check user roles and permissions
        
        # Basic checks
        if not current_user.is_active:
            raise AuthorizationException("User account is inactive")
        
        if not current_user.is_verified:
            raise AuthorizationException("User account is not verified")
        
        # TODO: Implement proper role-based permission checking
        # For now, all authenticated users have basic permissions
        basic_permissions = [
            "user:read",
            "dream:read",
            "dream:write",
            "analysis:read",
            "analysis:execute",
            "statistics:read"
        ]
        
        if self.required_permission not in basic_permissions:
            raise AuthorizationException(f"Insufficient permissions: {self.required_permission}")
        
        return current_user


def require_permission(permission: str):
    """
    Create a permission requirement dependency
    """
    return PermissionChecker(permission)


class PaginationParams:
    """Pagination parameters dependency"""
    
    def __init__(
        self,
        page: int = 1,
        limit: int = 20
    ):
        if page < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Page number must be greater than 0"
            )
        
        if limit < 1 or limit > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Limit must be between 1 and 100"
            )
        
        self.page = page
        self.limit = limit
        self.offset = (page - 1) * limit


def get_pagination_params(
    page: int = 1,
    limit: int = 20
) -> PaginationParams:
    """
    Get pagination parameters
    """
    return PaginationParams(page=page, limit=limit)


class FilterParams:
    """Common filter parameters"""
    
    def __init__(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        sort: str = "created_at_desc"
    ):
        self.start_date = start_date
        self.end_date = end_date
        self.sort = sort
        
        # Validate sort parameter
        valid_sorts = [
            "created_at_desc", "created_at_asc",
            "updated_at_desc", "updated_at_asc",
            "date_desc", "date_asc"
        ]
        
        if sort not in valid_sorts:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid sort parameter. Must be one of: {', '.join(valid_sorts)}"
            )


def get_filter_params(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    sort: str = "created_at_desc"
) -> FilterParams:
    """
    Get filter parameters
    """
    return FilterParams(
        start_date=start_date,
        end_date=end_date,
        sort=sort
    )


async def validate_user_access(
    user_id: str,
    current_user: User = Depends(get_current_user)
) -> str:
    """
    Validate that current user can access the specified user's data
    """
    # Users can only access their own data
    if current_user.user_id != user_id:
        raise AuthorizationException("Access denied: You can only access your own data")
    
    return user_id


async def validate_dream_access(
    dream_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> str:
    """
    Validate that current user can access the specified dream
    """
    from app.services.dream_service import DreamService
    
    dream_service = DreamService(db)
    dream = await dream_service.get_dream_by_id(dream_id)
    
    if not dream:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Dream not found"
        )
    
    # Users can only access their own dreams
    if dream.user_id != current_user.user_id:
        raise AuthorizationException("Access denied: You can only access your own dreams")
    
    return dream_id
