"""
User schemas
"""

from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
import re

from app.core.config import settings


class UserBase(BaseModel):
    """Base user schema"""
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr


class UserCreate(UserBase):
    """User creation schema"""
    password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH, max_length=settings.PASSWORD_MAX_LENGTH)
    device_id: Optional[str] = Field(None, max_length=100)
    
    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('Username can only contain letters, numbers, and underscores')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserUpdate(BaseModel):
    """User update schema"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    real_name: Optional[str] = Field(None, max_length=100)
    avatar_url: Optional[str] = Field(None, max_length=500)
    timezone: Optional[str] = Field(None, max_length=50)
    language: Optional[str] = Field(None, max_length=10)
    
    @validator('username')
    def validate_username(cls, v):
        if v and not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('Username can only contain letters, numbers, and underscores')
        return v


class UserLogin(BaseModel):
    """User login schema"""
    email: EmailStr
    password: str = Field(..., min_length=1)
    device_id: Optional[str] = Field(None, max_length=100)


class UserResponse(UserBase):
    """User response schema"""
    user_id: str
    real_name: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: bool
    is_verified: bool
    last_login: Optional[datetime] = None
    email_verified_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    settings: Dict[str, Any] = {}
    statistics: Dict[str, Any] = {}
    
    class Config:
        from_attributes = True


class UserProfile(UserResponse):
    """Extended user profile schema"""
    device_info: Dict[str, Any] = {}
    registration_ip: Optional[str] = None
    dream_count: int = 0
    analysis_count: int = 0
    login_count: int = 0


class TokenResponse(BaseModel):
    """Token response schema"""
    user_id: str
    username: str
    token: str
    expires_in: int
    last_login: Optional[datetime] = None


class RefreshTokenRequest(BaseModel):
    """Refresh token request schema"""
    refresh_token: str


class PasswordChangeRequest(BaseModel):
    """Password change request schema"""
    current_password: str
    new_password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH, max_length=settings.PASSWORD_MAX_LENGTH)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v


class PasswordResetRequest(BaseModel):
    """Password reset request schema"""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Password reset confirmation schema"""
    token: str
    new_password: str = Field(..., min_length=settings.PASSWORD_MIN_LENGTH, max_length=settings.PASSWORD_MAX_LENGTH)
    
    @validator('new_password')
    def validate_new_password(cls, v):
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v


class EmailVerificationRequest(BaseModel):
    """Email verification request schema"""
    email: EmailStr


class EmailVerificationConfirm(BaseModel):
    """Email verification confirmation schema"""
    token: str


class UserExportRequest(BaseModel):
    """User data export request schema"""
    format: str = Field(..., regex=r'^(json|csv)$')
    include_analysis: bool = True
    start_date: Optional[str] = Field(None, regex=r'^\d{4}-\d{2}-\d{2}$')
    end_date: Optional[str] = Field(None, regex=r'^\d{4}-\d{2}-\d{2}$')


class UserExportResponse(BaseModel):
    """User data export response schema"""
    export_id: str
    status: str
    estimated_time: Optional[int] = None
    download_url: Optional[str] = None


class UserDeleteRequest(BaseModel):
    """User account deletion request schema"""
    password: str
    confirmation: str = Field(..., regex=r'^DELETE$')


class UserStatistics(BaseModel):
    """User statistics schema"""
    total_dreams: int
    total_analyzed: int
    usage_days: int
    member_since_days: int
    avg_dreams_per_day: float
    health_score: Optional[float] = None
    mood_trend: Optional[str] = None


class UserSettings(BaseModel):
    """User settings schema"""
    timezone: str = "Asia/Shanghai"
    language: str = "zh-CN"
    notification_enabled: bool = True
    privacy_mode: bool = False
    theme: str = "light"
    
    class Config:
        extra = "allow"  # Allow additional settings
