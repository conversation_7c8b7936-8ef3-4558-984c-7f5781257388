"""
Dream model
"""

from sqlalchemy import String, Text, Date, <PERSON><PERSON><PERSON>, Integer, Foreign<PERSON>ey, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship
from datetime import date
from typing import Optional, List
import uuid
import enum

from app.core.database import Base


class DreamStatus(str, enum.Enum):
    """Dream record status"""
    PENDING_ANALYSIS = "pending_analysis"
    ANALYZING = "analyzing"
    ANALYZED = "analyzed"
    ANALYSIS_FAILED = "analysis_failed"


class Dream(Base):
    """Dream record model"""
    
    __tablename__ = "dreams"
    
    # Primary key
    dream_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"dream_{uuid.uuid4().hex[:12]}"
    )
    
    # Foreign key
    user_id: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("users.user_id"),
        nullable=False,
        index=True
    )
    
    # Dream information
    dream_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    emotions: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    
    # Status
    status: Mapped[DreamStatus] = mapped_column(
        SQLEnum(DreamStatus),
        default=DreamStatus.PENDING_ANALYSIS,
        nullable=False,
        index=True
    )
    
    # Metadata
    timezone: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    word_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Quality metrics
    quality_score: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 0-100
    
    # Content flags
    flags: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True, default=list)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="dreams")
    analysis: Mapped[Optional["DreamAnalysis"]] = relationship(
        "DreamAnalysis",
        back_populates="dream",
        uselist=False,
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Dream(dream_id='{self.dream_id}', user_id='{self.user_id}', date='{self.dream_date}')>"
    
    @property
    def description_preview(self) -> str:
        """Get preview of dream description (first 100 characters)"""
        if len(self.description) <= 100:
            return self.description
        return self.description[:97] + "..."
    
    @property
    def is_analyzed(self) -> bool:
        """Check if dream has been analyzed"""
        return self.status == DreamStatus.ANALYZED and self.analysis is not None
    
    @property
    def can_be_analyzed(self) -> bool:
        """Check if dream can be analyzed"""
        return self.status in [DreamStatus.PENDING_ANALYSIS, DreamStatus.ANALYSIS_FAILED]
    
    def to_dict(self, include_analysis: bool = False) -> dict:
        """Convert dream to dictionary"""
        data = {
            "dream_id": self.dream_id,
            "user_id": self.user_id,
            "dream_date": self.dream_date.isoformat(),
            "description": self.description,
            "description_preview": self.description_preview,
            "emotions": self.emotions or [],
            "status": self.status.value,
            "timezone": self.timezone,
            "word_count": self.word_count,
            "quality_score": self.quality_score,
            "flags": self.flags or [],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
        
        if include_analysis and self.analysis:
            data["analysis"] = self.analysis.to_dict()
        
        return data


class DreamKeyword(Base):
    """Dream keyword model for search optimization"""
    
    __tablename__ = "dream_keywords"
    
    # Primary key
    keyword_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"keyword_{uuid.uuid4().hex[:12]}"
    )
    
    # Foreign key
    dream_id: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("dreams.dream_id"),
        nullable=False,
        index=True
    )
    
    # Keyword information
    keyword: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    weight: Mapped[float] = mapped_column(default=1.0, nullable=False)  # Keyword importance weight
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # nature, emotion, action, etc.
    
    def __repr__(self) -> str:
        return f"<DreamKeyword(keyword='{self.keyword}', dream_id='{self.dream_id}', weight={self.weight})>"


class EmotionTag(Base):
    """Emotion tag configuration model"""
    
    __tablename__ = "emotion_tags"
    
    # Primary key
    tag_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"emotion_{uuid.uuid4().hex[:12]}"
    )
    
    # Tag information
    name: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    display_name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Classification
    category: Mapped[str] = mapped_column(String(50), nullable=False)  # positive, negative, neutral
    color: Mapped[Optional[str]] = mapped_column(String(7), nullable=True)  # Hex color code
    
    # Status
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    sort_order: Mapped[int] = mapped_column(default=0, nullable=False)
    
    # Usage statistics
    usage_count: Mapped[int] = mapped_column(default=0, nullable=False)
    
    def __repr__(self) -> str:
        return f"<EmotionTag(name='{self.name}', category='{self.category}')>"
    
    def to_dict(self) -> dict:
        """Convert emotion tag to dictionary"""
        return {
            "tag_id": self.tag_id,
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "category": self.category,
            "color": self.color,
            "is_active": self.is_active,
            "sort_order": self.sort_order,
            "usage_count": self.usage_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
