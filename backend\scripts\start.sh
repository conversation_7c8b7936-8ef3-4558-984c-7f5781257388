#!/bin/bash

# LotusDream API Startup Script

set -e

echo "🌸 Starting LotusDream API..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your configuration before running again."
    exit 1
fi

# Load environment variables
source .env

echo "🔧 Environment: $ENVIRONMENT"

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    
    echo "⏳ Waiting for $service_name to be ready..."
    while ! nc -z $host $port; do
        sleep 1
    done
    echo "✅ $service_name is ready!"
}

# Development mode
if [ "$ENVIRONMENT" = "development" ]; then
    echo "🚀 Starting in development mode..."
    
    # Start services with docker-compose
    if command -v docker-compose &> /dev/null; then
        echo "📦 Starting services with docker-compose..."
        docker-compose up -d postgres redis
        
        # Wait for services
        wait_for_service localhost 5432 "PostgreSQL"
        wait_for_service localhost 6379 "Redis"
        
        echo "🔄 Running database migrations..."
        python -m alembic upgrade head
        
        echo "🌱 Starting API server..."
        uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    else
        echo "❌ docker-compose not found. Please install Docker and docker-compose."
        exit 1
    fi

# Production mode
elif [ "$ENVIRONMENT" = "production" ]; then
    echo "🏭 Starting in production mode..."
    
    # Start all services
    docker-compose up -d
    
    echo "✅ All services started!"
    echo "📊 API: http://localhost:8000"
    echo "📈 Flower: http://localhost:5555"
    echo "🌐 Nginx: http://localhost:80"

# Testing mode
elif [ "$ENVIRONMENT" = "testing" ]; then
    echo "🧪 Starting in testing mode..."
    
    # Start test database
    docker-compose up -d postgres redis
    
    # Wait for services
    wait_for_service localhost 5432 "PostgreSQL"
    wait_for_service localhost 6379 "Redis"
    
    echo "🔄 Running database migrations..."
    python -m alembic upgrade head
    
    echo "🧪 Running tests..."
    pytest -v --cov=app --cov-report=html

else
    echo "❌ Unknown environment: $ENVIRONMENT"
    echo "Valid environments: development, production, testing"
    exit 1
fi
