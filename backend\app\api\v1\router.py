"""
API v1 Router
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, dreams, analysis, statistics, users

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(dreams.router, prefix="/dreams", tags=["Dreams"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["Analysis"])
api_router.include_router(statistics.router, prefix="/statistics", tags=["Statistics"])
api_router.include_router(users.router, prefix="/user", tags=["User Management"])
