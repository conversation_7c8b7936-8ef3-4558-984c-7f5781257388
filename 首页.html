<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LotusDream - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar {
            display: none;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: black;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(100% - 44px - 83px);
            padding: 20px;
            overflow-y: auto;
        }
        .content-area::-webkit-scrollbar {
            display: none;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 12px;
        }
        .nav-item.active {
            color: #667eea;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .emotion-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            margin: 4px;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .emotion-tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .emotion-tag.selected {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .submit-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 16px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        .input-area {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .char-count {
            text-align: right;
            color: #9ca3af;
            font-size: 12px;
            margin-top: 8px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup mx-auto">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="content-area">
                <!-- 标题区域 -->
                <div class="text-center mb-8">
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">LotusDream</h1>
                    <p class="text-gray-600">记录你的梦境，探索内心世界</p>
                </div>

                <!-- 日期选择 -->
                <div class="input-area">
                    <label class="block text-sm font-medium text-gray-700 mb-2">记录日期</label>
                    <input type="date" value="2024-01-15" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                </div>

                <!-- 情绪选择 -->
                <div class="input-area">
                    <label class="block text-sm font-medium text-gray-700 mb-3">选择情绪 (1-3个)</label>
                    <div class="flex flex-wrap">
                        <span class="emotion-tag selected">平静</span>
                        <span class="emotion-tag">焦虑</span>
                        <span class="emotion-tag">快乐</span>
                        <span class="emotion-tag">恐惧</span>
                        <span class="emotion-tag">愤怒</span>
                        <span class="emotion-tag">悲伤</span>
                        <span class="emotion-tag">兴奋</span>
                        <span class="emotion-tag">困惑</span>
                    </div>
                </div>

                <!-- 梦境描述 -->
                <div class="input-area">
                    <label class="block text-sm font-medium text-gray-700 mb-2">梦境描述</label>
                    <textarea 
                        placeholder="请详细描述你的梦境内容..."
                        class="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                        maxlength="300"
                    >我梦见自己在一个美丽的花园里，到处都是盛开的莲花...</textarea>
                    <div class="char-count">45/300</div>
                </div>

                <!-- 提交按钮 -->
                <button class="submit-btn">
                    <i class="fas fa-magic mr-2"></i>
                    开始解析梦境
                </button>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item active">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>解析</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-history"></i>
                    <span>历史</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>统计</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
