"""
Dream analysis model
"""

from sqlalchemy import String, Text, JSON, Float, Foreign<PERSON>ey, Enum as S<PERSON><PERSON><PERSON>, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship
from datetime import datetime
from typing import Optional, List, Dict, Any
import uuid
import enum

from app.core.database import Base


class AnalysisStatus(str, enum.Enum):
    """Analysis status"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class AnalysisType(str, enum.Enum):
    """Analysis type"""
    QUICK = "quick"
    COMPREHENSIVE = "comprehensive"


class DreamAnalysis(Base):
    """Dream analysis model"""
    
    __tablename__ = "dream_analyses"
    
    # Primary key
    analysis_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"analysis_{uuid.uuid4().hex[:12]}"
    )
    
    # Foreign keys
    dream_id: Mapped[str] = mapped_column(
        String(50),
        Foreign<PERSON>ey("dreams.dream_id"),
        nullable=False,
        unique=True,
        index=True
    )
    user_id: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("users.user_id"),
        nullable=False,
        index=True
    )
    
    # Analysis configuration
    analysis_type: Mapped[AnalysisType] = mapped_column(
        SQLEnum(AnalysisType),
        default=AnalysisType.COMPREHENSIVE,
        nullable=False
    )
    
    # Status
    status: Mapped[AnalysisStatus] = mapped_column(
        SQLEnum(AnalysisStatus),
        default=AnalysisStatus.PENDING,
        nullable=False,
        index=True
    )
    
    # Analysis results
    keywords: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True, default=list)
    psychological_hints: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    symbolic_meaning: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    suggestions: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True, default=list)
    
    # Poetry quote
    poetry_quote: Mapped[Optional[Dict[str, str]]] = mapped_column(JSON, nullable=True)
    
    # Quality metrics
    confidence_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # 0.0-1.0
    quality_rating: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-5 stars
    
    # Processing information
    processing_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # seconds
    model_version: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Timestamps
    started_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    
    # Relationships
    dream: Mapped["Dream"] = relationship("Dream", back_populates="analysis")
    user: Mapped["User"] = relationship("User", back_populates="analyses")
    
    def __repr__(self) -> str:
        return f"<DreamAnalysis(analysis_id='{self.analysis_id}', dream_id='{self.dream_id}', status='{self.status}')>"
    
    @property
    def is_completed(self) -> bool:
        """Check if analysis is completed"""
        return self.status == AnalysisStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if analysis failed"""
        return self.status == AnalysisStatus.FAILED
    
    @property
    def is_processing(self) -> bool:
        """Check if analysis is in progress"""
        return self.status == AnalysisStatus.PROCESSING
    
    def to_dict(self) -> dict:
        """Convert analysis to dictionary"""
        return {
            "analysis_id": self.analysis_id,
            "dream_id": self.dream_id,
            "user_id": self.user_id,
            "analysis_type": self.analysis_type.value,
            "status": self.status.value,
            "keywords": self.keywords or [],
            "psychological_hints": self.psychological_hints,
            "symbolic_meaning": self.symbolic_meaning,
            "suggestions": self.suggestions or [],
            "poetry_quote": self.poetry_quote,
            "confidence_score": self.confidence_score,
            "quality_rating": self.quality_rating,
            "processing_time": self.processing_time,
            "model_version": self.model_version,
            "error_message": self.error_message,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class PoetryQuote(Base):
    """Poetry quote library model"""
    
    __tablename__ = "poetry_quotes"
    
    # Primary key
    quote_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"quote_{uuid.uuid4().hex[:12]}"
    )
    
    # Quote content
    content: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    author: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    source: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    
    # Classification
    category: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    keywords: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    emotion_tags: Mapped[List[str]] = mapped_column(JSON, nullable=False, default=list)
    
    # Status
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    
    # Usage statistics
    usage_count: Mapped[int] = mapped_column(default=0, nullable=False)
    rating: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # Average user rating
    
    def __repr__(self) -> str:
        return f"<PoetryQuote(quote_id='{self.quote_id}', category='{self.category}')>"
    
    def to_dict(self) -> dict:
        """Convert poetry quote to dictionary"""
        return {
            "quote_id": self.quote_id,
            "content": self.content,
            "description": self.description,
            "author": self.author,
            "source": self.source,
            "category": self.category,
            "keywords": self.keywords or [],
            "emotion_tags": self.emotion_tags or [],
            "is_active": self.is_active,
            "usage_count": self.usage_count,
            "rating": self.rating,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class AnalysisTask(Base):
    """Analysis task queue model"""
    
    __tablename__ = "analysis_tasks"
    
    # Primary key
    task_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"task_{uuid.uuid4().hex[:12]}"
    )
    
    # Foreign keys
    dream_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    user_id: Mapped[str] = mapped_column(String(50), nullable=False, index=True)
    
    # Task configuration
    analysis_type: Mapped[AnalysisType] = mapped_column(
        SQLEnum(AnalysisType),
        default=AnalysisType.COMPREHENSIVE,
        nullable=False
    )
    priority: Mapped[int] = mapped_column(default=0, nullable=False)  # Higher number = higher priority
    
    # Status
    status: Mapped[AnalysisStatus] = mapped_column(
        SQLEnum(AnalysisStatus),
        default=AnalysisStatus.PENDING,
        nullable=False,
        index=True
    )
    
    # Processing information
    worker_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    retry_count: Mapped[int] = mapped_column(default=0, nullable=False)
    max_retries: Mapped[int] = mapped_column(default=3, nullable=False)
    
    # Timestamps
    scheduled_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    started_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    
    # Error information
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    def __repr__(self) -> str:
        return f"<AnalysisTask(task_id='{self.task_id}', dream_id='{self.dream_id}', status='{self.status}')>"
    
    @property
    def can_retry(self) -> bool:
        """Check if task can be retried"""
        return self.retry_count < self.max_retries and self.status == AnalysisStatus.FAILED
    
    def to_dict(self) -> dict:
        """Convert analysis task to dictionary"""
        return {
            "task_id": self.task_id,
            "dream_id": self.dream_id,
            "user_id": self.user_id,
            "analysis_type": self.analysis_type.value,
            "priority": self.priority,
            "status": self.status.value,
            "worker_id": self.worker_id,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
