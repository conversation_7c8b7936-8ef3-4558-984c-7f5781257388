version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: lotusdream_postgres
    environment:
      POSTGRES_DB: lotusdream
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - lotusdream_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: lotusdream_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - lotusdream_network

  # FastAPI Application
  api:
    build: .
    container_name: lotusdream_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/lotusdream
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=True
      - ENVIRONMENT=development
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - lotusdream_network
    restart: unless-stopped

  # Celery Worker (for background tasks)
  celery_worker:
    build: .
    container_name: lotusdream_celery_worker
    command: celery -A app.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/lotusdream
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=your-super-secret-key-change-this-in-production
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
      - api
    networks:
      - lotusdream_network
    restart: unless-stopped

  # Celery Beat (for scheduled tasks)
  celery_beat:
    build: .
    container_name: lotusdream_celery_beat
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/lotusdream
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - SECRET_KEY=your-super-secret-key-change-this-in-production
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
      - api
    networks:
      - lotusdream_network
    restart: unless-stopped

  # Flower (Celery monitoring)
  flower:
    build: .
    container_name: lotusdream_flower
    command: celery -A app.tasks.celery_app flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      - redis
    networks:
      - lotusdream_network
    restart: unless-stopped

  # Nginx (reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: lotusdream_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./uploads:/var/www/uploads
    depends_on:
      - api
    networks:
      - lotusdream_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  lotusdream_network:
    driver: bridge
