# LotusDream 管理后台 - API接口文档

## 📋 接口概述

**项目名称**：LotusDream 管理后台 API  
**版本**：v1.0.0  
**基础URL**：`https://admin-api.lotusdream.com/v1`  
**认证方式**：JWT Token + 权限验证  
**数据格式**：JSON  

## 🔐 认证说明

所有接口请求需要在Header中携带认证信息：
```
Authorization: Bearer {admin_jwt_token}
Content-Type: application/json
X-Admin-Role: {role_name}
```

## 📱 接口分类

### 1. 管理员认证模块
### 2. 仪表盘数据模块
### 3. 用户管理模块
### 4. 梦境数据管理模块
### 5. AI解析管理模块
### 6. 数据分析模块
### 7. 内容管理模块
### 8. 系统管理模块
### 9. 运营工具模块

---

## 🔑 1. 管理员认证模块

### 1.1 管理员登录
**接口地址**：`POST /admin/auth/login`  
**接口描述**：管理员登录获取Token

**请求参数**：
```json
{
  "username": "admin",
  "password": "password123",
  "captcha": "ABCD",
  "captcha_key": "captcha_12345"
}
```

**响应示例**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "admin_id": "admin_001",
    "username": "admin",
    "real_name": "系统管理员",
    "role": "super_admin",
    "permissions": ["user:read", "user:write", "system:config"],
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 28800,
    "last_login": "2024-01-15T10:30:00Z"
  }
}
```

### 1.2 刷新Token
**接口地址**：`POST /admin/auth/refresh`  
**接口描述**：刷新管理员Token

### 1.3 获取管理员信息
**接口地址**：`GET /admin/auth/profile`  
**接口描述**：获取当前登录管理员信息

---

## 📊 2. 仪表盘数据模块

### 2.1 获取关键指标
**接口地址**：`GET /admin/dashboard/metrics`  
**接口描述**：获取仪表盘关键业务指标

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_users": {
      "value": 12580,
      "growth_rate": 0.15,
      "compared_to": "last_month"
    },
    "active_users": {
      "value": 3420,
      "growth_rate": 0.08,
      "compared_to": "last_week"
    },
    "dream_records": {
      "value": 856,
      "growth_rate": 0.23,
      "compared_to": "yesterday"
    },
    "analysis_completed": {
      "value": 742,
      "success_rate": 0.95,
      "compared_to": "yesterday"
    }
  }
}
```

### 2.2 获取用户增长趋势
**接口地址**：`GET /admin/dashboard/user-growth`  
**接口描述**：获取用户增长趋势数据

**请求参数**：
```
period: string    // 时间周期：7d, 30d, 90d
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "period": "30d",
    "growth_data": [
      {"date": "2024-01-01", "new_users": 45, "total_users": 12000},
      {"date": "2024-01-02", "new_users": 52, "total_users": 12052},
      {"date": "2024-01-03", "new_users": 38, "total_users": 12090}
    ],
    "summary": {
      "total_new_users": 1580,
      "average_daily": 52.7,
      "growth_rate": 0.15
    }
  }
}
```

### 2.3 获取实时监控数据
**接口地址**：`GET /admin/dashboard/realtime`  
**接口描述**：获取实时系统监控数据

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "online_users": 234,
    "analysis_queue": 12,
    "system_status": {
      "cpu_usage": 0.45,
      "memory_usage": 0.67,
      "disk_usage": 0.23
    },
    "error_count": 3,
    "last_updated": "2024-01-15T10:35:00Z"
  }
}
```

---

## 👥 3. 用户管理模块

### 3.1 获取用户列表
**接口地址**：`GET /admin/users`  
**接口描述**：获取用户列表（支持分页和筛选）

**请求参数**：
```
page: int           // 页码，默认1
limit: int          // 每页数量，默认20
sort: string        // 排序：created_at_desc, login_at_desc
status: string      // 状态筛选：active, disabled, deleted
keyword: string     // 搜索关键词
start_date: string  // 注册开始日期
end_date: string    // 注册结束日期
tags: string        // 用户标签筛选
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 12580,
    "page": 1,
    "limit": 20,
    "total_pages": 629,
    "users": [
      {
        "user_id": "user_12345",
        "username": "dreamuser",
        "email": "<EMAIL>",
        "real_name": "张三",
        "status": "active",
        "dream_count": 42,
        "last_login": "2024-01-15T09:30:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "tags": ["活跃用户", "VIP用户"]
      }
    ]
  }
}
```

### 3.2 获取用户详情
**接口地址**：`GET /admin/users/{user_id}`  
**接口描述**：获取指定用户的详细信息

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user_info": {
      "user_id": "user_12345",
      "username": "dreamuser",
      "email": "<EMAIL>",
      "real_name": "张三",
      "avatar_url": "https://example.com/avatar.jpg",
      "status": "active",
      "created_at": "2024-01-01T00:00:00Z",
      "last_login": "2024-01-15T09:30:00Z",
      "login_count": 156,
      "tags": ["活跃用户", "VIP用户"]
    },
    "statistics": {
      "dream_count": 42,
      "analysis_count": 38,
      "usage_days": 28,
      "avg_dreams_per_day": 1.5
    },
    "recent_activities": [
      {
        "action": "create_dream",
        "timestamp": "2024-01-15T08:30:00Z",
        "details": "创建梦境记录"
      }
    ]
  }
}
```

### 3.3 更新用户状态
**接口地址**：`PUT /admin/users/{user_id}/status`  
**接口描述**：更新用户账号状态

**请求参数**：
```json
{
  "status": "disabled",
  "reason": "违规内容",
  "note": "用户发布不当内容，暂时禁用账号"
}
```

### 3.4 批量操作用户
**接口地址**：`POST /admin/users/batch`  
**接口描述**：批量操作用户（禁用、启用、添加标签等）

**请求参数**：
```json
{
  "user_ids": ["user_001", "user_002", "user_003"],
  "action": "add_tags",
  "params": {
    "tags": ["测试用户", "重点关注"]
  }
}
```

---

## 🌙 4. 梦境数据管理模块

### 4.1 获取梦境记录列表
**接口地址**：`GET /admin/dreams`  
**接口描述**：获取梦境记录列表

**请求参数**：
```
page: int           // 页码
limit: int          // 每页数量
user_id: string     // 用户ID筛选
status: string      // 解析状态筛选
emotion: string     // 情绪筛选
keyword: string     // 内容关键词搜索
start_date: string  // 开始日期
end_date: string    // 结束日期
quality_min: int    // 最低质量分
quality_max: int    // 最高质量分
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 45620,
    "page": 1,
    "limit": 20,
    "dreams": [
      {
        "dream_id": "dream_67890",
        "user_id": "user_12345",
        "username": "dreamuser",
        "dream_date": "2024-01-15",
        "emotions": ["平静", "快乐"],
        "description_preview": "我梦见自己在一个美丽的花园里...",
        "analysis_status": "completed",
        "quality_score": 85,
        "created_at": "2024-01-15T08:30:00Z",
        "flags": ["sensitive_content"]
      }
    ]
  }
}
```

### 4.2 获取梦境详情
**接口地址**：`GET /admin/dreams/{dream_id}`  
**接口描述**：获取梦境记录详细信息

### 4.3 更新梦境记录
**接口地址**：`PUT /admin/dreams/{dream_id}`  
**接口描述**：更新梦境记录（内容审核、质量评分等）

### 4.4 批量审核梦境
**接口地址**：`POST /admin/dreams/batch-review`  
**接口描述**：批量审核梦境记录

---

## 🧠 5. AI解析管理模块

### 5.1 获取解析任务列表
**接口地址**：`GET /admin/analysis/tasks`  
**接口描述**：获取AI解析任务列表

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 1250,
    "tasks": [
      {
        "task_id": "task_001",
        "dream_id": "dream_67890",
        "user_id": "user_12345",
        "status": "processing",
        "created_at": "2024-01-15T10:30:00Z",
        "started_at": "2024-01-15T10:31:00Z",
        "estimated_completion": "2024-01-15T10:32:00Z",
        "processing_time": 45,
        "error_message": null
      }
    ],
    "queue_stats": {
      "pending": 23,
      "processing": 5,
      "completed": 1200,
      "failed": 22
    }
  }
}
```

### 5.2 获取解析质量统计
**接口地址**：`GET /admin/analysis/quality-stats`  
**接口描述**：获取解析质量统计数据

### 5.3 重新执行解析
**接口地址**：`POST /admin/analysis/{task_id}/retry`  
**接口描述**：重新执行失败的解析任务

---

## 📈 6. 数据分析模块

### 6.1 获取用户行为分析
**接口地址**：`GET /admin/analytics/user-behavior`  
**接口描述**：获取用户行为分析数据

**请求参数**：
```
period: string      // 分析周期：7d, 30d, 90d
dimension: string   // 分析维度：activity, retention, feature_usage
```

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "period": "30d",
    "dimension": "activity",
    "activity_data": [
      {"date": "2024-01-01", "active_users": 1200, "new_users": 45},
      {"date": "2024-01-02", "active_users": 1180, "new_users": 52}
    ],
    "summary": {
      "avg_daily_active": 1150,
      "peak_active": 1350,
      "activity_trend": "stable"
    }
  }
}
```

### 6.2 获取产品使用统计
**接口地址**：`GET /admin/analytics/product-usage`  
**接口描述**：获取产品功能使用统计

### 6.3 生成自定义报表
**接口地址**：`POST /admin/analytics/custom-report`  
**接口描述**：生成自定义数据报表

---

## 🎨 7. 内容管理模块

### 7.1 获取诗意句子库
**接口地址**：`GET /admin/content/poems`  
**接口描述**：获取诗意句子库列表

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 1580,
    "poems": [
      {
        "id": "poem_001",
        "content": "莲花不染淤泥，心境自然清明",
        "description": "愿你如莲花般，在浮世中保持内心的纯净",
        "category": "nature",
        "keywords": ["莲花", "纯净", "宁静"],
        "emotion_tags": ["平静", "安详"],
        "usage_count": 156,
        "rating": 4.8,
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 7.2 创建诗意句子
**接口地址**：`POST /admin/content/poems`  
**接口描述**：创建新的诗意句子

### 7.3 更新诗意句子
**接口地址**：`PUT /admin/content/poems/{poem_id}`  
**接口描述**：更新诗意句子信息

### 7.4 获取情绪标签配置
**接口地址**：`GET /admin/content/emotion-tags`  
**接口描述**：获取情绪标签配置

---

## ⚙️ 8. 系统管理模块

### 8.1 获取管理员列表
**接口地址**：`GET /admin/system/admins`  
**接口描述**：获取管理员账号列表

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 15,
    "admins": [
      {
        "admin_id": "admin_001",
        "username": "admin",
        "real_name": "系统管理员",
        "email": "<EMAIL>",
        "role": "super_admin",
        "status": "active",
        "last_login": "2024-01-15T10:30:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 8.2 创建管理员账号
**接口地址**：`POST /admin/system/admins`  
**接口描述**：创建新的管理员账号

### 8.3 获取角色权限配置
**接口地址**：`GET /admin/system/roles`  
**接口描述**：获取角色权限配置

### 8.4 获取操作日志
**接口地址**：`GET /admin/system/logs`  
**接口描述**：获取系统操作日志

---

## 🛠️ 9. 运营工具模块

### 9.1 发送消息推送
**接口地址**：`POST /admin/operations/push-message`  
**接口描述**：发送消息推送

**请求参数**：
```json
{
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-24:00进行维护",
  "target_type": "all",
  "target_users": [],
  "push_time": "2024-01-15T20:00:00Z",
  "channels": ["app", "email"]
}
```

### 9.2 获取用户反馈列表
**接口地址**：`GET /admin/operations/feedback`  
**接口描述**：获取用户反馈列表

### 9.3 处理用户反馈
**接口地址**：`PUT /admin/operations/feedback/{feedback_id}`  
**接口描述**：处理用户反馈

### 9.4 获取工单列表
**接口地址**：`GET /admin/operations/tickets`  
**接口描述**：获取工单列表

---

## 📝 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": [
      {
        "field": "username",
        "message": "用户名不能为空"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 状态码说明
- `200` - 请求成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权/Token无效
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率限制
- `500` - 服务器内部错误

---

## 🔒 安全说明

### 1. 权限验证
- 所有接口需要管理员Token验证
- 基于角色的权限控制
- 操作权限细粒度控制
- 敏感操作二次验证

### 2. 数据安全
- 敏感数据脱敏处理
- 用户隐私信息保护
- 数据访问日志记录
- 数据导出权限控制

### 3. 接口安全
- 请求频率限制
- IP白名单控制
- 参数严格验证
- SQL注入防护

---

## 📞 技术支持

**开发团队**：LotusDream Admin Team  
**技术文档**：https://admin-docs.lotusdream.com  
**问题反馈**：<EMAIL>  
**更新日志**：https://admin-changelog.lotusdream.com
