"""
User service
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, func
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import structlog

from app.models.user import User, UserSession, UserLoginLog
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import security
from app.core.redis import cache, CacheKeys
from app.core.config import settings
from app.core.exceptions import UserNotFoundException

logger = structlog.get_logger()


class UserService:
    """User service class"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_user(
        self,
        user_data: UserCreate,
        registration_ip: Optional[str] = None
    ) -> User:
        """Create a new user"""
        
        # Hash password
        password_hash = security.hash_password(user_data.password)
        
        # Create user instance
        user = User(
            username=user_data.username,
            email=user_data.email,
            password_hash=password_hash,
            registration_ip=registration_ip,
            device_info={"device_id": user_data.device_id} if user_data.device_id else {},
            settings={
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "notification_enabled": True,
                "privacy_mode": False
            }
        )
        
        # Add to database
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        logger.info("User created", user_id=user.user_id, email=user.email)
        
        return user
    
    async def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        
        # Try cache first
        cache_key = CacheKeys.user_info(user_id)
        cached_user = await cache.get(cache_key)
        
        if cached_user:
            # Convert cached data back to User object
            # Note: This is simplified - in production you'd want proper serialization
            pass
        
        # Query database
        result = await self.db.execute(
            select(User).where(and_(User.user_id == user_id, User.is_deleted == False))
        )
        user = result.scalar_one_or_none()
        
        if user:
            # Cache user info
            await cache.set(
                cache_key,
                user.to_dict(include_sensitive=False),
                ttl=settings.CACHE_TTL_USER_INFO
            )
        
        return user
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        
        result = await self.db.execute(
            select(User).where(and_(User.email == email, User.is_deleted == False))
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        
        result = await self.db.execute(
            select(User).where(and_(User.username == username, User.is_deleted == False))
        )
        return result.scalar_one_or_none()
    
    async def update_user(self, user_id: str, user_data: UserUpdate) -> User:
        """Update user information"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)
        
        # Update fields
        update_data = user_data.dict(exclude_unset=True)
        
        if update_data:
            await self.db.execute(
                update(User)
                .where(User.user_id == user_id)
                .values(**update_data)
            )
            await self.db.commit()
            
            # Invalidate cache
            cache_key = CacheKeys.user_info(user_id)
            await cache.delete(cache_key)
            
            # Refresh user
            await self.db.refresh(user)
        
        logger.info("User updated", user_id=user_id, fields=list(update_data.keys()))
        
        return user
    
    async def update_last_login(self, user_id: str) -> None:
        """Update user's last login timestamp"""
        
        await self.db.execute(
            update(User)
            .where(User.user_id == user_id)
            .values(
                last_login=datetime.utcnow(),
                login_count=User.login_count + 1
            )
        )
        await self.db.commit()
        
        # Invalidate cache
        cache_key = CacheKeys.user_info(user_id)
        await cache.delete(cache_key)
    
    async def update_user_settings(self, user_id: str, settings: Dict[str, Any]) -> User:
        """Update user settings"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)
        
        # Merge with existing settings
        current_settings = user.settings or {}
        current_settings.update(settings)
        
        await self.db.execute(
            update(User)
            .where(User.user_id == user_id)
            .values(settings=current_settings)
        )
        await self.db.commit()
        
        # Invalidate cache
        cache_key = CacheKeys.user_info(user_id)
        await cache.delete(cache_key)
        
        # Refresh user
        await self.db.refresh(user)
        
        logger.info("User settings updated", user_id=user_id)
        
        return user
    
    async def deactivate_user(self, user_id: str, reason: Optional[str] = None) -> User:
        """Deactivate user account"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)
        
        await self.db.execute(
            update(User)
            .where(User.user_id == user_id)
            .values(is_active=False)
        )
        await self.db.commit()
        
        # Invalidate cache
        cache_key = CacheKeys.user_info(user_id)
        await cache.delete(cache_key)
        
        # Refresh user
        await self.db.refresh(user)
        
        logger.info("User deactivated", user_id=user_id, reason=reason)
        
        return user
    
    async def delete_user(self, user_id: str) -> None:
        """Soft delete user account"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)
        
        await self.db.execute(
            update(User)
            .where(User.user_id == user_id)
            .values(
                is_deleted=True,
                is_active=False,
                deleted_at=datetime.utcnow()
            )
        )
        await self.db.commit()
        
        # Invalidate cache
        cache_key = CacheKeys.user_info(user_id)
        await cache.delete(cache_key)
        
        logger.info("User deleted", user_id=user_id)
    
    async def get_user_statistics(self, user_id: str) -> Dict[str, Any]:
        """Get user statistics"""
        
        user = await self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundException(user_id)
        
        # Calculate member since days
        member_since = (datetime.utcnow() - user.created_at).days
        
        # Calculate average dreams per day
        avg_dreams_per_day = user.dream_count / max(member_since, 1)
        
        return {
            "total_dreams": user.dream_count,
            "total_analyzed": user.analysis_count,
            "usage_days": member_since,  # This should be calculated from actual usage
            "member_since_days": member_since,
            "avg_dreams_per_day": round(avg_dreams_per_day, 2),
            "login_count": user.login_count
        }
    
    async def search_users(
        self,
        keyword: Optional[str] = None,
        is_active: Optional[bool] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """Search users with filters"""
        
        # Build query
        query = select(User).where(User.is_deleted == False)
        
        if keyword:
            query = query.where(
                User.username.ilike(f"%{keyword}%") |
                User.email.ilike(f"%{keyword}%") |
                User.real_name.ilike(f"%{keyword}%")
            )
        
        if is_active is not None:
            query = query.where(User.is_active == is_active)
        
        # Count total
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # Apply pagination
        offset = (page - 1) * limit
        query = query.offset(offset).limit(limit)
        
        # Execute query
        result = await self.db.execute(query)
        users = result.scalars().all()
        
        return {
            "total": total,
            "page": page,
            "limit": limit,
            "total_pages": (total + limit - 1) // limit,
            "users": [user.to_dict() for user in users]
        }
