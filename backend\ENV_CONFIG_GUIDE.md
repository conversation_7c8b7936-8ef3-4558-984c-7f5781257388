# LotusDream 环境配置指南

## 🔧 必须配置的项目

### 1. SECRET_KEY（已更新，生产环境需要再次修改）
```bash
# 当前配置（开发环境可用）
SECRET_KEY=lotusdream_2024_super_secret_key_change_this_in_production_environment_12345

# 生产环境建议生成新的密钥
# 可以使用以下命令生成：
python -c "import secrets; print(secrets.token_urlsafe(32))"
```

### 2. OPENAI_API_KEY（需要替换为真实密钥）
```bash
# 当前配置（需要替换）
OPENAI_API_KEY=sk-your-openai-api-key-here-replace-with-real-key

# 获取 OpenAI API Key：
# 1. 访问 https://platform.openai.com/api-keys
# 2. 登录或注册 OpenAI 账号
# 3. 创建新的 API Key
# 4. 复制并替换上面的值

# 如果暂时不需要 AI 功能，可以保持默认值，但相关功能会报错
```

## 🔄 可选配置项目

### 3. 数据库配置
```bash
# 使用 Docker（推荐，无需修改）
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/lotusdream

# 使用外部数据库
# DATABASE_URL=postgresql+asyncpg://用户名:密码@数据库地址:端口/数据库名
# 例如：
# DATABASE_URL=postgresql+asyncpg://myuser:<EMAIL>:5432/lotusdream
```

### 4. Redis 配置
```bash
# 使用 Docker（推荐，无需修改）
REDIS_URL=redis://localhost:6379/0

# 使用外部 Redis
# REDIS_URL=redis://用户名:密码@redis地址:端口/数据库号
# 例如：
# REDIS_URL=redis://:<EMAIL>:6379/0
```

### 5. 邮件配置（可选）
```bash
# Gmail 配置示例
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password  # 使用应用专用密码，不是登录密码
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587

# QQ邮箱配置示例
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-authorization-code  # 使用授权码
# MAIL_SERVER=smtp.qq.com
# MAIL_PORT=587

# 163邮箱配置示例
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-authorization-code
# MAIL_SERVER=smtp.163.com
# MAIL_PORT=587

# 如果不需要邮件功能，保持默认值即可
```

### 6. CORS 配置（前端地址）
```bash
# 当前配置（适用于本地开发）
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# 如果前端运行在其他端口，添加对应地址：
# ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://localhost:5173"]

# 生产环境示例：
# ALLOWED_ORIGINS=["https://lotusdream.com", "https://app.lotusdream.com"]
```

## 🚀 快速配置步骤

### 最小配置（可以运行项目）
1. **SECRET_KEY** - 已配置 ✅
2. **数据库和Redis** - 使用Docker默认配置 ✅
3. **其他配置** - 保持默认值 ✅

### 完整配置（包含所有功能）
1. **获取 OpenAI API Key**：
   ```bash
   # 访问 https://platform.openai.com/api-keys
   # 注册并创建 API Key
   # 替换 .env 中的 OPENAI_API_KEY
   ```

2. **配置邮件服务**（如果需要）：
   ```bash
   # 以 Gmail 为例：
   # 1. 开启两步验证
   # 2. 生成应用专用密码
   # 3. 更新 MAIL_USERNAME 和 MAIL_PASSWORD
   ```

## 🔍 配置验证

### 检查配置是否正确
```bash
# 1. 检查必需的环境变量
grep -E "SECRET_KEY|DATABASE_URL|REDIS_URL" .env

# 2. 启动项目测试
docker-compose up -d

# 3. 检查健康状态
curl http://localhost:8000/health

# 4. 查看日志
docker-compose logs api
```

### 常见配置问题

1. **数据库连接失败**
   ```bash
   # 检查数据库是否启动
   docker-compose ps postgres
   
   # 检查连接
   docker-compose exec postgres psql -U postgres -d lotusdream -c "SELECT 1;"
   ```

2. **Redis 连接失败**
   ```bash
   # 检查 Redis 是否启动
   docker-compose ps redis
   
   # 检查连接
   docker-compose exec redis redis-cli ping
   ```

3. **OpenAI API 错误**
   ```bash
   # 测试 API Key 是否有效
   curl -H "Authorization: Bearer YOUR_API_KEY" https://api.openai.com/v1/models
   ```

## 🌍 不同环境配置

### 开发环境（当前配置）
```bash
ENVIRONMENT=development
DEBUG=True
LOG_LEVEL=INFO
```

### 生产环境
```bash
ENVIRONMENT=production
DEBUG=False
LOG_LEVEL=WARNING
SECRET_KEY=生成新的强密钥
DATABASE_URL=生产数据库地址
REDIS_URL=生产Redis地址
ALLOWED_ORIGINS=["https://yourdomain.com"]
```

### 测试环境
```bash
ENVIRONMENT=testing
DEBUG=True
DATABASE_URL=测试数据库地址
REDIS_URL=测试Redis地址
```

## 📋 配置检查清单

- [ ] SECRET_KEY 已设置（不是默认值）
- [ ] DATABASE_URL 正确配置
- [ ] REDIS_URL 正确配置
- [ ] OPENAI_API_KEY 已设置（如需AI功能）
- [ ] MAIL_* 配置已设置（如需邮件功能）
- [ ] ALLOWED_ORIGINS 包含前端地址
- [ ] 端口 8000, 5432, 6379 未被占用
- [ ] Docker 服务正常运行

## 🆘 获取帮助

如果配置过程中遇到问题：

1. **查看日志**：`docker-compose logs api`
2. **检查服务状态**：`docker-compose ps`
3. **重启服务**：`docker-compose restart`
4. **完全重建**：`docker-compose down && docker-compose up -d`

配置完成后，你就可以正常运行 LotusDream 项目了！🚀
