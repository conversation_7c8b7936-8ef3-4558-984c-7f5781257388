#!/bin/bash

# LotusDream 快速修复脚本

echo "🔧 LotusDream 快速修复..."

# 停止所有容器
echo "⏹️  停止现有容器..."
docker-compose down 2>/dev/null || true

# 清理 Docker 资源
echo "🧹 清理 Docker 资源..."
docker system prune -f

# 使用简化版 docker-compose
echo "🚀 使用简化配置启动核心服务..."
docker-compose -f docker-compose-simple.yml down
docker-compose -f docker-compose-simple.yml build --no-cache
docker-compose -f docker-compose-simple.yml up -d

echo "⏳ 等待服务启动..."
sleep 30

echo "📊 检查服务状态..."
docker-compose -f docker-compose-simple.yml ps

echo "🔍 测试服务连接..."
echo "数据库连接测试："
docker-compose -f docker-compose-simple.yml exec postgres pg_isready -U postgres || echo "❌ 数据库未就绪"

echo "Redis 连接测试："
docker-compose -f docker-compose-simple.yml exec redis redis-cli ping || echo "❌ Redis 未就绪"

echo "API 健康检查："
curl -f http://localhost:8000/health 2>/dev/null && echo "✅ API 正常" || echo "❌ API 未就绪"

echo ""
echo "✅ 修复完成！"
echo "📝 接下来可以："
echo "1. 访问 API 文档: http://localhost:8000/docs"
echo "2. 检查 API 健康: http://localhost:8000/health"
echo "3. 查看日志: docker-compose -f docker-compose-simple.yml logs api"
