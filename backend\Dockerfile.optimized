# 优化的 Dockerfile - 利用缓存层
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

# 设置工作目录
WORKDIR /app

# 安装系统依赖（这层很少变化，会被缓存）
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
        curl \
    && rm -rf /var/lib/apt/lists/*

# 先复制依赖文件（这层只有依赖变化时才重建）
COPY requirements-minimal.txt .

# 安装 Python 依赖（这层会被缓存，除非依赖文件变化）
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements-minimal.txt

# 创建用户（这层会被缓存）
RUN adduser --disabled-password --gecos '' appuser

# 创建目录（这层会被缓存）
RUN mkdir -p uploads logs \
    && chown -R appuser:appuser /app

# 最后复制代码（只有代码变化时这层才重建）
COPY . .
RUN chown -R appuser:appuser /app

USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 运行应用
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
