<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LotusDream - 梦境历史</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar {
            display: none;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: black;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        .header {
            height: 60px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .search-bar {
            height: 50px;
            padding: 0 20px;
            display: flex;
            align-items: center;
        }
        .content-area {
            height: calc(100% - 44px - 60px - 50px - 83px);
            padding: 0 20px 20px;
            overflow-y: auto;
        }
        .content-area::-webkit-scrollbar {
            display: none;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 12px;
        }
        .nav-item.active {
            color: #667eea;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .dream-card {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .dream-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        .emotion-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 10px;
            margin: 2px;
            display: inline-block;
        }
        .search-input {
            background: white;
            border-radius: 20px;
            padding: 8px 16px;
            border: none;
            flex: 1;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .filter-btn {
            background: white;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            color: #667eea;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup mx-auto">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 头部导航 -->
            <div class="header">
                <h2 class="text-lg font-semibold text-gray-800">梦境历史</h2>
                <i class="fas fa-ellipsis-v text-gray-600"></i>
            </div>

            <!-- 搜索栏 -->
            <div class="search-bar">
                <input type="text" placeholder="搜索梦境关键词..." class="search-input">
                <button class="filter-btn">
                    <i class="fas fa-filter"></i>
                </button>
            </div>

            <!-- 主要内容区域 -->
            <div class="content-area">
                <!-- 梦境记录卡片 -->
                <div class="dream-card">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">2024年1月15日</span>
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    </div>
                    <div class="mb-2">
                        <span class="emotion-tag">平静</span>
                        <span class="emotion-tag">快乐</span>
                    </div>
                    <p class="text-sm text-gray-700 leading-relaxed">
                        我梦见自己在一个美丽的花园里，到处都是盛开的莲花，阳光透过树叶洒在花瓣上...
                    </p>
                    <div class="flex justify-between items-center mt-3">
                        <span class="text-xs text-purple-600">已解析</span>
                        <span class="text-xs text-gray-400">关键词: 莲花, 花园, 宁静</span>
                    </div>
                </div>

                <div class="dream-card">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">2024年1月14日</span>
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    </div>
                    <div class="mb-2">
                        <span class="emotion-tag">焦虑</span>
                        <span class="emotion-tag">困惑</span>
                    </div>
                    <p class="text-sm text-gray-700 leading-relaxed">
                        梦见自己在一个迷宫中迷路了，四周都是高墙，找不到出口，心情很焦虑...
                    </p>
                    <div class="flex justify-between items-center mt-3">
                        <span class="text-xs text-purple-600">已解析</span>
                        <span class="text-xs text-gray-400">关键词: 迷宫, 迷路, 焦虑</span>
                    </div>
                </div>

                <div class="dream-card">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">2024年1月13日</span>
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    </div>
                    <div class="mb-2">
                        <span class="emotion-tag">兴奋</span>
                        <span class="emotion-tag">快乐</span>
                    </div>
                    <p class="text-sm text-gray-700 leading-relaxed">
                        梦见自己在天空中飞翔，俯瞰着美丽的大地，感觉非常自由和快乐...
                    </p>
                    <div class="flex justify-between items-center mt-3">
                        <span class="text-xs text-purple-600">已解析</span>
                        <span class="text-xs text-gray-400">关键词: 飞翔, 自由, 天空</span>
                    </div>
                </div>

                <div class="dream-card">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">2024年1月12日</span>
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    </div>
                    <div class="mb-2">
                        <span class="emotion-tag">悲伤</span>
                    </div>
                    <p class="text-sm text-gray-700 leading-relaxed">
                        梦见了已故的奶奶，她对我微笑着，但我知道这只是梦境，醒来后很想念她...
                    </p>
                    <div class="flex justify-between items-center mt-3">
                        <span class="text-xs text-purple-600">已解析</span>
                        <span class="text-xs text-gray-400">关键词: 亲人, 思念, 回忆</span>
                    </div>
                </div>

                <div class="dream-card">
                    <div class="flex justify-between items-start mb-2">
                        <span class="text-xs text-gray-500">2024年1月11日</span>
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    </div>
                    <div class="mb-2">
                        <span class="emotion-tag">恐惧</span>
                        <span class="emotion-tag">紧张</span>
                    </div>
                    <p class="text-sm text-gray-700 leading-relaxed">
                        梦见自己在考试，但完全不会做题，时间快到了，非常紧张和害怕...
                    </p>
                    <div class="flex justify-between items-center mt-3">
                        <span class="text-xs text-purple-600">已解析</span>
                        <span class="text-xs text-gray-400">关键词: 考试, 压力, 焦虑</span>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>解析</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-history"></i>
                    <span>历史</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>统计</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
