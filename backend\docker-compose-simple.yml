# 简化版 Docker Compose - 只启动核心服务

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: lotusdream_postgres
    environment:
      POSTGRES_DB: lotusdream
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - lotusdream_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: lotusdream_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - lotusdream_network

  # FastAPI Application
  api:
    build: .
    container_name: lotusdream_api
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/lotusdream
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=lotusdream_2024_super_secret_key_change_this_in_production_environment_12345
      - DEBUG=True
      - ENVIRONMENT=development
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - lotusdream_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  lotusdream_network:
    driver: bridge
