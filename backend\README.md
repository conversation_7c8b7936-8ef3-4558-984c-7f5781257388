# LotusDream API Backend

LotusDream APP 后台 API 服务，基于 FastAPI + PostgreSQL + Redis 构建的高性能梦境分析平台。

## 🌟 功能特性

- **用户认证系统**：JWT Token 认证，支持注册、登录、Token 刷新
- **梦境记录管理**：创建、更新、删除梦境记录
- **AI 梦境解析**：集成 OpenAI GPT 进行梦境分析
- **历史记录查询**：支持搜索、筛选、分页
- **数据统计分析**：用户行为分析、情绪趋势、关键词云图
- **缓存优化**：Redis 缓存提升性能
- **异步处理**：Celery 后台任务队列
- **API 文档**：自动生成的 OpenAPI 文档
- **容器化部署**：Docker + docker-compose 一键部署

## 🏗️ 技术架构

### 后端技术栈
- **框架**：FastAPI 0.104.1
- **数据库**：PostgreSQL 15 + SQLAlchemy 2.0 (异步)
- **缓存**：Redis 7
- **任务队列**：Celery + Redis
- **认证**：JWT Token
- **API 文档**：OpenAPI/Swagger
- **容器化**：Docker + docker-compose

### 项目结构
```
backend/
├── app/
│   ├── api/                    # API 路由
│   │   └── v1/
│   │       ├── endpoints/      # API 端点
│   │       └── router.py       # 路由配置
│   ├── core/                   # 核心配置
│   │   ├── config.py          # 应用配置
│   │   ├── database.py        # 数据库配置
│   │   ├── redis.py           # Redis 配置
│   │   ├── security.py        # 安全工具
│   │   └── exceptions.py      # 异常定义
│   ├── models/                 # 数据模型
│   │   ├── user.py            # 用户模型
│   │   ├── dream.py           # 梦境模型
│   │   ├── analysis.py        # 分析模型
│   │   └── statistics.py      # 统计模型
│   ├── schemas/                # Pydantic 模式
│   │   ├── user.py            # 用户模式
│   │   └── dream.py           # 梦境模式
│   ├── services/               # 业务逻辑
│   │   ├── user_service.py    # 用户服务
│   │   ├── dream_service.py   # 梦境服务
│   │   └── analysis_service.py # 分析服务
│   ├── middleware/             # 中间件
│   │   ├── rate_limit.py      # 限流中间件
│   │   └── logging.py         # 日志中间件
│   └── main.py                # 应用入口
├── scripts/                    # 脚本文件
│   └── start.sh               # 启动脚本
├── requirements.txt            # Python 依赖
├── Dockerfile                 # Docker 配置
├── docker-compose.yml         # Docker Compose 配置
└── README.md                  # 项目文档
```

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 1. 克隆项目
```bash
git clone <repository-url>
cd backend
```

### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

### 3. 启动服务

#### 开发模式
```bash
# 使用启动脚本
chmod +x scripts/start.sh
./scripts/start.sh

# 或手动启动
docker-compose up -d postgres redis
pip install -r requirements.txt
uvicorn app.main:app --reload
```

#### 生产模式
```bash
# 设置环境变量
export ENVIRONMENT=production

# 启动所有服务
./scripts/start.sh
```

### 4. 访问服务
- **API 文档**：http://localhost:8000/docs
- **API 服务**：http://localhost:8000
- **Flower 监控**：http://localhost:5555
- **健康检查**：http://localhost:8000/health

## 📚 API 文档

### 认证接口
- `POST /v1/auth/register` - 用户注册
- `POST /v1/auth/login` - 用户登录
- `POST /v1/auth/refresh` - 刷新 Token
- `POST /v1/auth/logout` - 用户登出

### 梦境管理
- `POST /v1/dreams` - 创建梦境记录
- `GET /v1/dreams/history` - 获取梦境历史
- `GET /v1/dreams/{dream_id}` - 获取梦境详情
- `PUT /v1/dreams/{dream_id}` - 更新梦境记录
- `DELETE /v1/dreams/{dream_id}` - 删除梦境记录

### 梦境解析
- `POST /v1/dreams/{dream_id}/analyze` - 提交梦境解析
- `GET /v1/dreams/{dream_id}/analysis` - 获取解析结果
- `POST /v1/dreams/{dream_id}/analysis/save` - 保存解析结果

### 数据统计
- `GET /v1/statistics/overview` - 获取统计概览
- `GET /v1/statistics/frequency` - 获取梦境频率统计
- `GET /v1/statistics/emotions` - 获取情绪趋势
- `GET /v1/statistics/keywords` - 获取关键词云数据

### 用户管理
- `GET /v1/user/profile` - 获取用户信息
- `PUT /v1/user/profile` - 更新用户信息
- `POST /v1/user/export` - 导出用户数据
- `DELETE /v1/user/account` - 删除用户账号

## 🔧 配置说明

### 环境变量
```bash
# 应用配置
APP_NAME=LotusDream API
DEBUG=True
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/lotusdream

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# JWT 配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# AI 配置
OPENAI_API_KEY=your-openai-key
```

### 数据库迁移
```bash
# 生成迁移文件
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head
```

## 🧪 测试

### 运行测试
```bash
# 单元测试
pytest

# 测试覆盖率
pytest --cov=app --cov-report=html

# 集成测试
pytest tests/integration/
```

### API 测试
```bash
# 使用 httpie 测试
http POST localhost:8000/v1/auth/register username=test email=<EMAIL> password=Test123456

# 使用 curl 测试
curl -X POST "http://localhost:8000/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"Test123456"}'
```

## 📊 监控和日志

### 应用监控
- **健康检查**：`/health` 端点
- **Prometheus 指标**：`/metrics` 端点
- **Celery 监控**：Flower Web UI

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f api

# 查看 Celery 日志
docker-compose logs -f celery_worker

# 查看数据库日志
docker-compose logs -f postgres
```

## 🔒 安全特性

- **JWT Token 认证**：无状态认证机制
- **密码加密**：bcrypt 哈希加密
- **请求限流**：Redis 实现的限流机制
- **CORS 配置**：跨域请求控制
- **SQL 注入防护**：SQLAlchemy ORM 防护
- **XSS 防护**：输入验证和输出编码

## 🚀 部署指南

### Docker 部署
```bash
# 构建镜像
docker build -t lotusdream-api .

# 运行容器
docker run -d -p 8000:8000 lotusdream-api
```

### Kubernetes 部署
```bash
# 应用 K8s 配置
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -l app=lotusdream-api
```

### 生产环境优化
- 使用 Gunicorn + Uvicorn workers
- 配置 Nginx 反向代理
- 启用 SSL/TLS 加密
- 配置数据库连接池
- 设置 Redis 集群

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- **技术支持**：<EMAIL>
- **问题反馈**：[GitHub Issues](https://github.com/lotusdream/api/issues)
- **文档**：[API 文档](https://docs.lotusdream.com)

---

**LotusDream Team** - 探索梦境，发现内心 🌸
