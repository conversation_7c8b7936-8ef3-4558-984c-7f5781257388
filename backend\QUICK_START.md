# 🚀 LotusDream 快速启动指南

## ❌ 问题解决

你遇到的错误是由于依赖包版本冲突导致的。我已经为你创建了修复方案。

## 🔧 解决方案

### 方案一：使用修复脚本（推荐）

```bash
# 1. 进入项目目录
cd backend

# 2. 运行修复脚本
chmod +x fix_dependencies.sh
./fix_dependencies.sh
```

### 方案二：手动修复

```bash
# 1. 停止所有容器
docker-compose down

# 2. 清理 Docker 缓存
docker system prune -f

# 3. 重新构建（不使用缓存）
docker-compose build --no-cache

# 4. 启动基础服务
docker-compose up -d postgres redis

# 5. 等待服务启动（约30秒）
sleep 30

# 6. 启动 API 服务
docker-compose up -d api

# 7. 检查状态
docker-compose ps
```

### 方案三：最小化启动

如果上述方案仍有问题，使用最小化配置：

```bash
# 1. 只启动数据库和 Redis
docker-compose up -d postgres redis

# 2. 本地运行 API（需要先安装依赖）
pip install -r requirements-minimal.txt
uvicorn app.main:app --reload
```

## 📋 验证步骤

### 1. 检查服务状态
```bash
docker-compose ps
```

应该看到：
- postgres: Up
- redis: Up  
- api: Up

### 2. 检查健康状态
```bash
curl http://localhost:8000/health
```

应该返回：
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "environment": "development"
}
```

### 3. 访问 API 文档
打开浏览器访问：http://localhost:8000/docs

## 🐛 常见问题

### 1. 端口占用
```bash
# 检查端口占用
netstat -tulpn | grep :8000
netstat -tulpn | grep :5432
netstat -tulpn | grep :6379

# 如果有占用，可以修改 docker-compose.yml 中的端口
```

### 2. Docker 权限问题（Linux）
```bash
sudo usermod -aG docker $USER
# 重新登录或重启
```

### 3. 内存不足
```bash
# 清理 Docker 资源
docker system prune -a
```

### 4. 网络问题
```bash
# 重建网络
docker-compose down
docker network prune
docker-compose up -d
```

## 📝 修改说明

我对项目做了以下修改来解决依赖问题：

1. **创建了 `requirements-minimal.txt`**：
   - 只包含核心依赖
   - 移除了可能冲突的包
   - 使用版本范围而不是固定版本

2. **修改了 `Dockerfile`**：
   - 使用最小化依赖文件
   - 减少构建时间和错误

3. **创建了修复脚本**：
   - 自动清理和重建
   - 简化操作流程

## 🎯 下一步

启动成功后，你可以：

1. **测试 API**：
   ```bash
   # 健康检查
   curl http://localhost:8000/health
   
   # 查看 API 文档
   # http://localhost:8000/docs
   ```

2. **查看日志**：
   ```bash
   docker-compose logs -f api
   ```

3. **开发调试**：
   ```bash
   # 进入容器
   docker-compose exec api bash
   
   # 查看数据库
   docker-compose exec postgres psql -U postgres -d lotusdream
   ```

## 📞 如果仍有问题

如果修复后仍有问题，请提供：

1. 错误日志：`docker-compose logs api`
2. 服务状态：`docker-compose ps`
3. 系统信息：`docker --version` 和 `docker-compose --version`

这样我可以提供更具体的解决方案。
