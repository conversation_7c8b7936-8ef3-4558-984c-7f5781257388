# Application Settings
APP_NAME=LotusDream API
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# Server Settings
HOST=0.0.0.0
PORT=8000

# Database Settings
DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/lotusdream
DATABASE_ECHO=False

# Redis Settings
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# JWT Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440
REFRESH_TOKEN_EXPIRE_DAYS=30

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# AI/ML Settings
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo
ANALYSIS_TIMEOUT=30

# Email Settings
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_TLS=True
MAIL_SSL=False

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Monitoring
SENTRY_DSN=your-sentry-dsn

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=6
PASSWORD_MAX_LENGTH=128

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# Cache Settings
CACHE_TTL_USER_INFO=3600
CACHE_TTL_STATISTICS=1800
CACHE_TTL_ANALYSIS=7200
