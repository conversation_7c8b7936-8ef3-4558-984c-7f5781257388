"""
Statistics model
"""

from sqlalchemy import String, Integer, Float, Date, JSON, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column
from datetime import date
from typing import Optional, Dict, Any
import uuid

from app.core.database import Base


class UserStatistics(Base):
    """User statistics model"""
    
    __tablename__ = "user_statistics"
    
    # Primary key
    stat_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"stat_{uuid.uuid4().hex[:12]}"
    )
    
    # Foreign key
    user_id: Mapped[str] = mapped_column(
        String(50),
        ForeignKey("users.user_id"),
        nullable=False,
        index=True
    )
    
    # Time period
    period_type: Mapped[str] = mapped_column(String(20), nullable=False, index=True)  # daily, weekly, monthly, yearly
    period_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Dream statistics
    dreams_created: Mapped[int] = mapped_column(default=0, nullable=False)
    dreams_analyzed: Mapped[int] = mapped_column(default=0, nullable=False)
    total_words: Mapped[int] = mapped_column(default=0, nullable=False)
    avg_quality_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Emotion statistics
    emotion_distribution: Mapped[Optional[Dict[str, int]]] = mapped_column(JSON, nullable=True, default=dict)
    top_emotions: Mapped[Optional[list]] = mapped_column(JSON, nullable=True, default=list)
    
    # Keyword statistics
    top_keywords: Mapped[Optional[list]] = mapped_column(JSON, nullable=True, default=list)
    keyword_categories: Mapped[Optional[Dict[str, int]]] = mapped_column(JSON, nullable=True, default=dict)
    
    # Usage statistics
    login_count: Mapped[int] = mapped_column(default=0, nullable=False)
    active_days: Mapped[int] = mapped_column(default=0, nullable=False)
    session_duration: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # Average session duration in minutes
    
    # Health metrics
    health_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # 0-100
    mood_trend: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # improving, stable, declining
    
    def __repr__(self) -> str:
        return f"<UserStatistics(user_id='{self.user_id}', period='{self.period_type}', date='{self.period_date}')>"
    
    def to_dict(self) -> dict:
        """Convert statistics to dictionary"""
        return {
            "stat_id": self.stat_id,
            "user_id": self.user_id,
            "period_type": self.period_type,
            "period_date": self.period_date.isoformat(),
            "dreams_created": self.dreams_created,
            "dreams_analyzed": self.dreams_analyzed,
            "total_words": self.total_words,
            "avg_quality_score": self.avg_quality_score,
            "emotion_distribution": self.emotion_distribution or {},
            "top_emotions": self.top_emotions or [],
            "top_keywords": self.top_keywords or [],
            "keyword_categories": self.keyword_categories or {},
            "login_count": self.login_count,
            "active_days": self.active_days,
            "session_duration": self.session_duration,
            "health_score": self.health_score,
            "mood_trend": self.mood_trend,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class GlobalStatistics(Base):
    """Global platform statistics model"""
    
    __tablename__ = "global_statistics"
    
    # Primary key
    stat_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"global_{uuid.uuid4().hex[:12]}"
    )
    
    # Time period
    period_type: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    period_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # User statistics
    total_users: Mapped[int] = mapped_column(default=0, nullable=False)
    new_users: Mapped[int] = mapped_column(default=0, nullable=False)
    active_users: Mapped[int] = mapped_column(default=0, nullable=False)
    retained_users: Mapped[int] = mapped_column(default=0, nullable=False)
    
    # Dream statistics
    total_dreams: Mapped[int] = mapped_column(default=0, nullable=False)
    dreams_created: Mapped[int] = mapped_column(default=0, nullable=False)
    dreams_analyzed: Mapped[int] = mapped_column(default=0, nullable=False)
    analysis_success_rate: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Performance metrics
    avg_analysis_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # seconds
    system_uptime: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # percentage
    error_rate: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # percentage
    
    # Popular content
    top_emotions: Mapped[Optional[list]] = mapped_column(JSON, nullable=True, default=list)
    top_keywords: Mapped[Optional[list]] = mapped_column(JSON, nullable=True, default=list)
    popular_quotes: Mapped[Optional[list]] = mapped_column(JSON, nullable=True, default=list)
    
    def __repr__(self) -> str:
        return f"<GlobalStatistics(period='{self.period_type}', date='{self.period_date}')>"
    
    def to_dict(self) -> dict:
        """Convert global statistics to dictionary"""
        return {
            "stat_id": self.stat_id,
            "period_type": self.period_type,
            "period_date": self.period_date.isoformat(),
            "total_users": self.total_users,
            "new_users": self.new_users,
            "active_users": self.active_users,
            "retained_users": self.retained_users,
            "total_dreams": self.total_dreams,
            "dreams_created": self.dreams_created,
            "dreams_analyzed": self.dreams_analyzed,
            "analysis_success_rate": self.analysis_success_rate,
            "avg_analysis_time": self.avg_analysis_time,
            "system_uptime": self.system_uptime,
            "error_rate": self.error_rate,
            "top_emotions": self.top_emotions or [],
            "top_keywords": self.top_keywords or [],
            "popular_quotes": self.popular_quotes or [],
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }


class KeywordStatistics(Base):
    """Keyword usage statistics model"""
    
    __tablename__ = "keyword_statistics"
    
    # Primary key
    keyword_stat_id: Mapped[str] = mapped_column(
        String(50),
        primary_key=True,
        default=lambda: f"kw_stat_{uuid.uuid4().hex[:12]}"
    )
    
    # Keyword information
    keyword: Mapped[str] = mapped_column(String(100), nullable=False, index=True)
    category: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True)
    
    # Time period
    period_type: Mapped[str] = mapped_column(String(20), nullable=False, index=True)
    period_date: Mapped[date] = mapped_column(Date, nullable=False, index=True)
    
    # Usage statistics
    usage_count: Mapped[int] = mapped_column(default=0, nullable=False)
    user_count: Mapped[int] = mapped_column(default=0, nullable=False)  # Number of unique users
    avg_weight: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Trend information
    growth_rate: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # Compared to previous period
    trend: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # rising, stable, falling
    
    def __repr__(self) -> str:
        return f"<KeywordStatistics(keyword='{self.keyword}', period='{self.period_type}', date='{self.period_date}')>"
    
    def to_dict(self) -> dict:
        """Convert keyword statistics to dictionary"""
        return {
            "keyword_stat_id": self.keyword_stat_id,
            "keyword": self.keyword,
            "category": self.category,
            "period_type": self.period_type,
            "period_date": self.period_date.isoformat(),
            "usage_count": self.usage_count,
            "user_count": self.user_count,
            "avg_weight": self.avg_weight,
            "growth_rate": self.growth_rate,
            "trend": self.trend,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
