# LotusDream APP - 接口概览表

## 📋 接口总览

| 模块 | 接口数量 | 核心功能 |
|------|----------|----------|
| 用户认证 | 3个 | 注册、登录、Token刷新 |
| 梦境记录 | 3个 | 创建、更新、删除梦境记录 |
| 梦境解析 | 3个 | 提交解析、获取结果、保存记录 |
| 历史记录 | 3个 | 历史列表、详情查看、搜索 |
| 数据统计 | 4个 | 概览、频率、情绪趋势、关键词云 |
| 用户设置 | 5个 | 用户信息、数据导出、账号管理 |
| **总计** | **21个** | **完整的APP后台支持** |

---

## 🔑 1. 用户认证模块 (3个接口)

| 接口名称 | 方法 | 路径 | 描述 | 优先级 |
|----------|------|------|------|--------|
| 用户注册 | POST | `/auth/register` | 新用户注册 | 高 |
| 用户登录 | POST | `/auth/login` | 用户登录获取Token | 高 |
| 刷新Token | POST | `/auth/refresh` | 刷新用户Token | 中 |

---

## 🌙 2. 梦境记录模块 (3个接口)

| 接口名称 | 方法 | 路径 | 描述 | 优先级 |
|----------|------|------|------|--------|
| 创建梦境记录 | POST | `/dreams` | 用户创建新的梦境记录 | 高 |
| 更新梦境记录 | PUT | `/dreams/{dream_id}` | 更新指定的梦境记录 | 中 |
| 删除梦境记录 | DELETE | `/dreams/{dream_id}` | 删除指定的梦境记录 | 中 |

---

## 🧠 3. 梦境解析模块 (3个接口)

| 接口名称 | 方法 | 路径 | 描述 | 优先级 |
|----------|------|------|------|--------|
| 提交梦境解析 | POST | `/dreams/{dream_id}/analyze` | 提交梦境进行AI解析 | 高 |
| 获取解析结果 | GET | `/dreams/{dream_id}/analysis` | 获取梦境解析结果 | 高 |
| 保存解析结果 | POST | `/dreams/{dream_id}/analysis/save` | 保存解析结果到历史记录 | 中 |

---

## 📚 4. 历史记录模块 (3个接口)

| 接口名称 | 方法 | 路径 | 描述 | 优先级 |
|----------|------|------|------|--------|
| 获取梦境历史列表 | GET | `/dreams/history` | 获取用户的梦境历史记录列表 | 高 |
| 获取梦境详情 | GET | `/dreams/{dream_id}` | 获取指定梦境的详细信息 | 高 |
| 搜索梦境记录 | GET | `/dreams/search` | 根据关键词搜索梦境记录 | 中 |

---

## 📊 5. 数据统计模块 (4个接口)

| 接口名称 | 方法 | 路径 | 描述 | 优先级 |
|----------|------|------|------|--------|
| 获取统计概览 | GET | `/statistics/overview` | 获取用户的统计数据概览 | 高 |
| 获取梦境频率统计 | GET | `/statistics/frequency` | 获取梦境记录频率统计数据 | 中 |
| 获取情绪趋势 | GET | `/statistics/emotions` | 获取情绪变化趋势数据 | 中 |
| 获取关键词云数据 | GET | `/statistics/keywords` | 获取关键词云图数据 | 中 |

---

## ⚙️ 6. 用户设置模块 (5个接口)

| 接口名称 | 方法 | 路径 | 描述 | 优先级 |
|----------|------|------|------|--------|
| 获取用户信息 | GET | `/user/profile` | 获取用户基本信息 | 高 |
| 更新用户信息 | PUT | `/user/profile` | 更新用户基本信息 | 中 |
| 数据导出 | POST | `/user/export` | 导出用户数据 | 低 |
| 获取导出状态 | GET | `/user/export/{export_id}` | 获取数据导出状态 | 低 |
| 删除用户账号 | DELETE | `/user/account` | 删除用户账号及所有数据 | 低 |

---

## 🚀 开发优先级建议

### 第一阶段 (MVP核心功能)
**优先级：高**
- ✅ 用户注册/登录
- ✅ 创建梦境记录
- ✅ 提交梦境解析
- ✅ 获取解析结果
- ✅ 获取梦境历史列表
- ✅ 获取梦境详情
- ✅ 获取用户信息
- ✅ 获取统计概览

### 第二阶段 (完善功能)
**优先级：中**
- ⏳ 更新/删除梦境记录
- ⏳ 保存解析结果
- ⏳ 搜索梦境记录
- ⏳ 刷新Token
- ⏳ 获取频率统计
- ⏳ 获取情绪趋势
- ⏳ 获取关键词云
- ⏳ 更新用户信息

### 第三阶段 (增值功能)
**优先级：低**
- 📋 数据导出
- 📋 获取导出状态
- 📋 删除用户账号

---

## 🔧 技术实现建议

### 后端技术栈
- **框架**：Node.js + Express / Python + FastAPI / Java + Spring Boot
- **数据库**：PostgreSQL (主数据) + Redis (缓存)
- **AI服务**：OpenAI GPT / 自建NLP模型
- **认证**：JWT Token
- **部署**：Docker + Kubernetes

### 数据库设计要点
```sql
-- 用户表
users (user_id, username, email, password_hash, created_at, settings)

-- 梦境记录表
dreams (dream_id, user_id, dream_date, emotions, description, status, created_at)

-- 解析结果表
dream_analysis (analysis_id, dream_id, keywords, psychological_hints, 
                symbolic_meaning, suggestions, poetry_quote, confidence_score)

-- 统计缓存表
user_statistics (user_id, period, stats_data, updated_at)
```

### API设计原则
1. **RESTful设计**：遵循REST规范
2. **统一响应格式**：code + message + data结构
3. **错误处理**：详细的错误码和错误信息
4. **分页支持**：列表接口支持分页
5. **版本控制**：URL中包含版本号 `/v1/`

### 安全考虑
1. **HTTPS加密**：所有接口使用HTTPS
2. **JWT认证**：无状态的Token认证
3. **数据加密**：敏感数据加密存储
4. **频率限制**：防止接口滥用
5. **输入验证**：严格的参数校验

---

## 📈 性能优化建议

### 缓存策略
- **用户信息**：Redis缓存，TTL 1小时
- **统计数据**：Redis缓存，TTL 30分钟
- **解析结果**：数据库 + Redis双重存储

### 数据库优化
- **索引设计**：user_id, dream_date, created_at等关键字段
- **分表策略**：按时间分表存储历史数据
- **读写分离**：主从数据库分离

### API优化
- **批量操作**：支持批量获取数据
- **字段选择**：支持指定返回字段
- **压缩传输**：启用Gzip压缩

---

## 📋 测试建议

### 单元测试
- 每个接口的正常流程测试
- 异常情况和边界条件测试
- 参数验证测试

### 集成测试
- 完整的用户使用流程测试
- 数据一致性测试
- 并发访问测试

### 性能测试
- 接口响应时间测试
- 并发用户数测试
- 数据库性能测试

---

## 📞 开发协作

### 文档维护
- API文档实时更新
- 变更日志记录
- 示例代码提供

### 版本管理
- 语义化版本号
- 向后兼容性保证
- 废弃接口提前通知

### 监控告警
- 接口可用性监控
- 响应时间监控
- 错误率监控
