"""
Dream schemas
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import date, datetime
import re


class DreamBase(BaseModel):
    """Base dream schema"""
    dream_date: date
    emotions: List[str] = Field(..., min_items=1, max_items=3)
    description: str = Field(..., min_length=10, max_length=300)
    timezone: Optional[str] = Field(None, max_length=50)
    
    @validator('emotions')
    def validate_emotions(cls, v):
        if not v:
            raise ValueError('At least one emotion must be selected')
        if len(v) > 3:
            raise ValueError('Maximum 3 emotions can be selected')
        # Remove duplicates while preserving order
        seen = set()
        unique_emotions = []
        for emotion in v:
            if emotion not in seen:
                seen.add(emotion)
                unique_emotions.append(emotion)
        return unique_emotions
    
    @validator('description')
    def validate_description(cls, v):
        # Remove excessive whitespace
        v = re.sub(r'\s+', ' ', v.strip())
        if len(v) < 10:
            raise ValueError('Dream description must be at least 10 characters long')
        return v


class DreamCreate(DreamBase):
    """Dream creation schema"""
    pass


class DreamUpdate(BaseModel):
    """Dream update schema"""
    dream_date: Optional[date] = None
    emotions: Optional[List[str]] = Field(None, min_items=1, max_items=3)
    description: Optional[str] = Field(None, min_length=10, max_length=300)
    
    @validator('emotions')
    def validate_emotions(cls, v):
        if v is not None:
            if not v:
                raise ValueError('At least one emotion must be selected')
            if len(v) > 3:
                raise ValueError('Maximum 3 emotions can be selected')
            # Remove duplicates while preserving order
            seen = set()
            unique_emotions = []
            for emotion in v:
                if emotion not in seen:
                    seen.add(emotion)
                    unique_emotions.append(emotion)
            return unique_emotions
        return v
    
    @validator('description')
    def validate_description(cls, v):
        if v is not None:
            # Remove excessive whitespace
            v = re.sub(r'\s+', ' ', v.strip())
            if len(v) < 10:
                raise ValueError('Dream description must be at least 10 characters long')
        return v


class DreamResponse(DreamBase):
    """Dream response schema"""
    dream_id: str
    user_id: str
    status: str
    word_count: int
    quality_score: Optional[int] = None
    flags: List[str] = []
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DreamDetail(DreamResponse):
    """Detailed dream response schema"""
    description_preview: str
    analysis: Optional[Dict[str, Any]] = None


class DreamListResponse(BaseModel):
    """Dream list response schema"""
    total: int
    page: int
    limit: int
    total_pages: int
    dreams: List[DreamResponse]


class DreamSearchRequest(BaseModel):
    """Dream search request schema"""
    q: str = Field(..., min_length=1, max_length=100)
    page: int = Field(1, ge=1)
    limit: int = Field(20, ge=1, le=100)


class DreamSearchResponse(BaseModel):
    """Dream search response schema"""
    total: int
    page: int
    limit: int
    keyword: str
    dreams: List[Dict[str, Any]]


class EmotionTagBase(BaseModel):
    """Base emotion tag schema"""
    name: str = Field(..., min_length=1, max_length=50)
    display_name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    category: str = Field(..., regex=r'^(positive|negative|neutral)$')
    color: Optional[str] = Field(None, regex=r'^#[0-9A-Fa-f]{6}$')


class EmotionTagCreate(EmotionTagBase):
    """Emotion tag creation schema"""
    is_active: bool = True
    sort_order: int = 0


class EmotionTagUpdate(BaseModel):
    """Emotion tag update schema"""
    display_name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    category: Optional[str] = Field(None, regex=r'^(positive|negative|neutral)$')
    color: Optional[str] = Field(None, regex=r'^#[0-9A-Fa-f]{6}$')
    is_active: Optional[bool] = None
    sort_order: Optional[int] = None


class EmotionTagResponse(EmotionTagBase):
    """Emotion tag response schema"""
    tag_id: str
    is_active: bool
    sort_order: int
    usage_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DreamKeywordResponse(BaseModel):
    """Dream keyword response schema"""
    keyword_id: str
    dream_id: str
    keyword: str
    weight: float
    category: Optional[str] = None
    
    class Config:
        from_attributes = True


class DreamStatistics(BaseModel):
    """Dream statistics schema"""
    total_dreams: int
    analyzed_dreams: int
    pending_dreams: int
    avg_quality_score: Optional[float] = None
    top_emotions: List[Dict[str, Any]] = []
    top_keywords: List[Dict[str, Any]] = []
    emotion_distribution: Dict[str, int] = {}


class DreamAnalysisRequest(BaseModel):
    """Dream analysis request schema"""
    analysis_type: str = Field("comprehensive", regex=r'^(quick|comprehensive)$')


class DreamAnalysisResponse(BaseModel):
    """Dream analysis response schema"""
    analysis_id: str
    dream_id: str
    status: str
    estimated_time: Optional[int] = None
