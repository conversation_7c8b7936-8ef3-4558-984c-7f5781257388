<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LotusDream - 个人中心</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        body::-webkit-scrollbar {
            display: none;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: black;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: #333;
            font-size: 14px;
            font-weight: 600;
        }
        .header {
            height: 60px;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        .content-area {
            height: calc(100% - 44px - 60px - 83px);
            padding: 20px;
            overflow-y: auto;
        }
        .content-area::-webkit-scrollbar {
            display: none;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 12px;
        }
        .nav-item.active {
            color: #667eea;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .profile-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 24px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 32px;
            color: #667eea;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin: 16px 0;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: white;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
        }
        .menu-section {
            background: white;
            border-radius: 16px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .menu-item:last-child {
            border-bottom: none;
        }
        .menu-item:hover {
            background: #f8fafc;
            margin: 0 -16px;
            padding-left: 16px;
            padding-right: 16px;
            border-radius: 8px;
        }
        .menu-left {
            display: flex;
            align-items: center;
        }
        .menu-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            color: white;
        }
        .menu-text {
            font-size: 14px;
            color: #374151;
        }
        .menu-arrow {
            color: #9ca3af;
            font-size: 12px;
        }
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup mx-auto">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 头部导航 -->
            <div class="header">
                <h2 class="text-lg font-semibold text-gray-800">个人中心</h2>
                <i class="fas fa-cog text-gray-600"></i>
            </div>

            <!-- 主要内容区域 -->
            <div class="content-area">
                <!-- 用户信息卡片 -->
                <div class="profile-card">
                    <div class="avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">梦境探索者</h3>
                    <p class="text-sm opacity-80 mb-4">开始探索内心世界的旅程</p>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">42</div>
                            <div class="stat-label">梦境记录</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">38</div>
                            <div class="stat-label">解析完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">28</div>
                            <div class="stat-label">使用天数</div>
                        </div>
                    </div>
                </div>

                <!-- 数据管理 -->
                <div class="menu-section">
                    <h4 class="section-title">数据管理</h4>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                                <i class="fas fa-download"></i>
                            </div>
                            <span class="menu-text">导出数据</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <i class="fas fa-upload"></i>
                            </div>
                            <span class="menu-text">备份恢复</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                                <i class="fas fa-trash"></i>
                            </div>
                            <span class="menu-text">清空数据</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                </div>

                <!-- 隐私设置 -->
                <div class="menu-section">
                    <h4 class="section-title">隐私设置</h4>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <span class="menu-text">隐私保护</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                                <i class="fas fa-lock"></i>
                            </div>
                            <span class="menu-text">应用锁定</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                </div>

                <!-- 帮助支持 -->
                <div class="menu-section">
                    <h4 class="section-title">帮助支持</h4>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #f97316, #ea580c);">
                                <i class="fas fa-question-circle"></i>
                            </div>
                            <span class="menu-text">使用帮助</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #84cc16, #65a30d);">
                                <i class="fas fa-book"></i>
                            </div>
                            <span class="menu-text">梦境解析指南</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                    <div class="menu-item">
                        <div class="menu-left">
                            <div class="menu-icon" style="background: linear-gradient(135deg, #ec4899, #db2777);">
                                <i class="fas fa-heart"></i>
                            </div>
                            <span class="menu-text">关于我们</span>
                        </div>
                        <i class="fas fa-chevron-right menu-arrow"></i>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="text-center mt-6">
                    <p class="text-xs text-gray-500">LotusDream v1.0.0</p>
                    <p class="text-xs text-gray-400 mt-1">探索梦境，发现内心</p>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>解析</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-history"></i>
                    <span>历史</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>统计</span>
                </div>
                <div class="nav-item active">
                    <i class="fas fa-user"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
