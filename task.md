# LotusDream APP - UI设计任务清单

## 🌟 项目目标

生成一套跨平台LotusDream APP 的 UI 设计图，并最终组合成 UI 展示页。

## 📦 核心功能

- 支持最小化

---

## ✅ 待办事项列表

### ⬜ 待办事项1：产品功能设计

- 初始信息：我是你的产品设计助手，现在请你告诉我，你想开发什么样的产品吧~
- 分析用户发送的信息，对不明确的细节进行追问
- 结合追问得到的答案，加以详细描述形成【产品设计文档.md】文件

---

### ⬜ 待办事项2：交互设计

- 结合【待办事项1】输出的最终功能，确定该产品包含的所有页面
- 按下列格式输出页面信息，并更新【产品设计文档.md】

```
<页面名称>
用途：<页面的主要作用>
核心功能：<列出该页面包含的主要功能>

```

---

### ⬜ 待办事项3：UI设计

- 根据【产品设计文档.md】，同时遵守【UI设计风格】和【UI设计规格】
- 为每个页面创建独立的 html 文件（尺寸：375x812，包含 mock up）
- 中断任务并提示用户输入“继续”以进入下一步

---

### ⬜ 待办事项4：提示用户输入“继续”指令

---

### ⬜ 待办事项5：创建 UI.html 文件

- 页面整体背景色为 `#f6f6f6`
- 使用 iframe 将所有页面以网格形式嵌入，布局如下：
    - 每个 iframe：宽度 400px，高度 850px
    - 背景色：`#f6f6f6`
- 保证每张设计图完整显示且不被遮挡

---

### ⬜ 待办事项6：自检所有页面 html 文件

检查每个页面是否符合以下规范：

- 强制隐藏所有滑动条
- 设计图尺寸为 375x812PX
- 信号栏与标题栏背景色一致（可为透明）
- 图标和样式使用推荐方式引入
- 底部 tab 栏为白色填充，100% 不透明

---

### ⬜ 待办事项7：检查 UI.html 文件

- 删除 iframe 以外的所有装饰性元素
- 所有 mock up 样式应包含在 iframe 内部，不应重复在 UI.html 中渲染

---

## 🎨 UI设计风格

- 优雅的清新主义美学与功能的完美平衡
- 清新柔和的渐变配色与品牌色系汪然一体
- 恰到好处的留白设计
- 轻盈通透的沉淀式体验
- 信息层级通过微妙的阴影过渡与模块化卡片布局清晰呈现
- 用户视线能自然聚焦核心功能
- 精心打磨的圆角
- 细腻的微交互
- 舒适的视觉比例
- 规范的间距

---

## 📀 UI设计规格

1. 单个设计图尺寸为 375x812PX，带有 mock up 模拟手机样式
2. 图标：使用在线矢量图标库
3. 图片：引用 Unsplash 图片地址
4. 样式引用：通过 `<link>` 标签引入 Tailwind CSS CDN
5. 状态栏显示时间、信号等模拟信息
6. 信号栏与标题栏背景色一致（可为透明）
7. 底部 tab 栏必须为白色填充，100% 不透明
8. 使用固定高度防止容器变形

---

## ⚠️ 注意事项

- mock up 的颜色只能使用黑色
- 所有 html 文件中都强制隐藏滑动条